#!/usr/bin/python3
import os, sys, argparse, re, shutil, time, getpass, ast, json, math, hashlib
from subprocess import Popen, getoutput, getstatusoutput, PIPE, STDOUT
import configparser
from itertools import product
import numpy as np
import scipy.interpolate
import xlsxwriter
from xlsxwriter.utility import xl_rowcol_to_cell, xl_range_abs


class GraphData:
    def __init__(self, data_bitrate, data_psnr_y, data_psnr_u, data_psnr_v,
                 data_ssim_y, data_ssim_u, data_ssim_v, data_vmaf):
        self.bitrate = data_bitrate
        self.psnr_y = data_psnr_y
        self.psnr_u = data_psnr_u
        self.psnr_v = data_psnr_v
        self.ssim_y = data_ssim_y
        self.ssim_u = data_ssim_u
        self.ssim_v = data_ssim_v
        self.vmaf = data_vmaf

def print_now(text):
	print('{}'.format(text))
	sys.stdout.flush()

new_dir = []
codec_map = {'0': 'h264', '1': 'h265', '2': 'av1'}
codecs_params_dict = {}
mctf_inputs = False
mctf_inputs_cqp = False
############### Preparation ###############

def get_config_value(configFile):
    global mctf_inputs
    global mctf_inputs_cqp
    config = configparser.ConfigParser(inline_comment_prefixes=";")
    config.optionxform = str
    config.read(configFile)
    codecs = config.get("Basic Parameters", "codec_output").split(',')
    crf = config.get("Basic Parameters", "crf").split(',') \
        if 'crf' in config.options('Basic Parameters') else ''
    simulation_type = config.get("Basic Parameters", "simulation_type").split(',')[0]
    mode = config.get("Basic Parameters", "mode").split(',')[0]
    encoder_codec =  [key for key, value in config.items('codec')][0]
    params_dict = {}
    for params in config.options('Xcoder Parameters'):
        params_dict[params] = config.get("Xcoder Parameters", params)
    for codec in codecs:
        codec = codec_map[codec].strip()
        codec_section = f'Xcoder Parameters:{codec}'
        codecs_params_dict[codec] = {}
        if codec_section in config:
            for key, value in config.items(codec_section):
                codecs_params_dict[codec][key] = value
    dir_sec = 'New Dir'
    if dir_sec in config:
        for dirs in config.options('New Dir'):
            if config.get("New Dir", dirs) == '1':
                new_dir.append(dirs)
    input_codec_section = 'Input Video Sequence:ext'
    if input_codec_section in config:
        for key,value in config.items(input_codec_section):
            if key == 'MCTF_inputs' and value == '1':
                mctf_inputs = True
            elif key == 'MCTF_inputs_CQP' and value == '1':
                mctf_inputs_cqp = True
    input_list = []
    for video in config.options('Input Video Sequence'):
        if config.get("Input Video Sequence", video) == '1':
            input_list.append(video)
    bitrate_list = []
    for bitr_val in config.options('Bitrate Sequence'):
        if config.get("Bitrate Sequence", bitr_val) == '1':
            bitrate_list.append(bitr_val)
    anchor_list = []
    for anchor in config.options('Anchor Sequence'):
        if config.get("Anchor Sequence", anchor) == '1':
            anchor_list.append(anchor)
    # keep the same rule as previous VIDIP test, when crf enable bitrate use default value
    crf_list = []
    if '1' in crf:
        for crf_val in config.options('Crf Sequence'):
            if config.get("Crf Sequence", crf_val) == '1':
                crf_list.append(crf_val)
    return codecs, crf, input_list, bitrate_list, anchor_list, crf_list, params_dict, simulation_type, mode, encoder_codec, mctf_inputs, mctf_inputs_cqp

def validate_config(file_path):
    sections_to_validate = {
        "Anchor Sequence": [],
        "Bitrate Sequence": [],
        "Input Video Sequence": [],
        "Basic Parameters": ["simulation_type", "mode"]
    }
    basic_parameters_to_validate = {
        "codec_output": lambda value: validate_codec_output(value)
    }

    def validate_codec_output(value):
        valid_values = {'0', '1', '2'}
        values = value.split(',')
        if all(v in valid_values for v in values):
            return True
        return False

    expected_codec_section = {
        "cnm_wave677": "1"
    }
    # Read the configuration file
    with open(file_path, 'r') as file:
        lines = file.readlines()

    current_section = None
    codec_section_content = {}
    errors = []

    for line in lines:
        # Remove comments and whitespace
        line = line.split(';')[0].strip()

        # Check for section headers
        section_match = re.match(r'\[(.*?)\]', line)
        if section_match:
            current_section = section_match.group(1)
            continue

        # Special validation for Basic Parameters
        if current_section == "Basic Parameters":
            parts = line.split('=')
            if len(parts) == 2:
                param, value = parts[0].strip(), parts[1].strip()
                if param in basic_parameters_to_validate:
                    if not basic_parameters_to_validate[param](value):
                        errors.append(f"Invalid value for {param} in section [{current_section}]: {value} (should be a subset of 0, 1, 2)")

        # Codec section validation
        elif current_section == "codec":
          parts = line.split('=')
          if len(parts) == 2:
            param, value = parts[0].strip(), parts[1].strip()
            codec_section_content[param] = value

        # If the current section is one we want to validate
        elif current_section in sections_to_validate:
            # Split the line into parameter and value
            parts = line.split('=')
            if len(parts) == 2:
                param, value = parts[0].strip(), parts[1].strip()
                # Check if the parameter is in the list of parameters to validate
                if not sections_to_validate[current_section] or param in sections_to_validate[current_section]:
                    # Validate the value
                    if value not in ['0', '1']:
                        errors.append(f"Invalid value for {param} in section [{current_section}]: {value} (should be 0 or 1)")

    for param, expected_value in expected_codec_section.items():
      if param not in codec_section_content or codec_section_content[param] != expected_value:
        errors.append(f"Modification detected in [codec] section: {param} should be {expected_value}")
    # Report results
    if errors:
        print_now("Validation failed with the following errors:")
        for error in errors:
            print_now(error)
            return False
    return True

def hash_func_single(file):
    BLOCK_SIZE = 65536
    file_hash = hashlib.sha256()
    with open(file, 'rb') as f:
        fb = f.read(BLOCK_SIZE)
        while len(fb) > 0:
            file_hash.update(fb)
            fb = f.read(BLOCK_SIZE)
    return file_hash.hexdigest()


def check_dataset_hash(group_name, video_path, vidip_ffmpeg, input_list):
    print('\ncheck_dataset_hash\n')
    with open("dataset_verification.json") as f:
        json_dict = json.load(f)
        ffmpeg_hash = hash_func_single(vidip_ffmpeg)
        if ffmpeg_hash != json_dict['ffmpeg']:
            print(
                'VIDIP FFmpeg binary file Hash does not match: Expected {} - Actual {} (FAILURE)'.format(
                    json_dict['ffmpeg'], ffmpeg_hash))
            return False
        files = json_dict[group_name]
        for file in input_list:
            file = file + '.yuv'
            e_hash = files[file]
            path = '{}/{}'.format(video_path, file)
            if not os.path.isfile(path):
                print('{} - Not found (FAILURE)'.format(path))
                return False
            a_hash = hash_func_single(path)
            if a_hash != e_hash:
                print(
                    'Hash does not match: Expected {} - Actual {} (FAILURE)'.format(e_hash, a_hash))
                return False
    return True


def ffmpeg_comamnd_prepare(crf, codecs, params_dict, user, codec_map, crf_list, input_list, bitrate_list,
                           video_path_map, cfg_file_name,
                           vidip_ffmpeg, vmaf_map):
    base_cmd = 'ffmpeg -y -vsync 0 -s {}x{} -r {} -i {} -c:v {}_ni_quadra_enc -xcoder-params {} -vframes {} {}'
    analysis_group = {}
    quality_cmd = '{} -r {} -i {} -s:v {}x{} -r {} -i {} ' \
                  '-filter_complex "[0:v][1:v]libvmaf=model="path={}":log_path=/home/<USER>/XCODER/{}:log_fmt=json;' \
                  '[0:v][1:v]psnr=stats_file=/home/<USER>/XCODER/{};[0:v][1:v]ssim=stats_file=/home/<USER>/XCODER/{}" -vframes {} -f null -'
    max_instance_map = {'3840x2160': 3, '1920x1080': 7, '1280x720': 9}
    params_limited = ['EnableRdoQuant', 'rdoLevel']
    width, height, fps = re.findall(r'(\d+)x(\d+)_?p(\d+)', input_list[0])[0]
    sub_path = video_path_map[width + 'x' + height] if width + 'x' + height != '3840x2160' else \
        video_path_map[width + 'x' + height]['p{}'.format(fps)]
    video_path = '/home/<USER>/sources/'.format(user) + sub_path
    max_instances = max_instance_map[width + 'x' + height]
    count = 0
    CMDs = []
    quality_CMDs = []
    sub_cmds = []
    sub_quality_cmds = []

    for crf_enable, codec, rdoq_val, rdolevel_val in product(crf, codecs, params_dict['EnableRdoQuant'],
                                                             params_dict['rdoLevel']):
        # h264 encoder just support rdolevel=1, av1 doesnt support EnableRdoQuant
        if codec_map[codec] == 'h264' and rdolevel_val != '1':
            continue
        if codec_map[codec] == 'av1' and rdoq_val == '1':
            continue
        group_name = 'crf{}_rdoq{}/rdolevel{}_{}'.format(crf_enable, rdoq_val, rdolevel_val, codec_map[codec])
        if not os.path.isdir('/home/<USER>/XCODER/{}'.format(user, group_name.split('/')[0])):
            getoutput('mkdir /home/<USER>/XCODER/{}'.format(user, group_name.split('/')[0]))
        if not os.path.isdir('/home/<USER>/XCODER/{}'.format(user, group_name)):
            getoutput('mkdir /home/<USER>/XCODER/{}'.format(user, group_name))
        analysis_group[group_name] = []

    for crf_enable in crf:
        # when enable crf, bitrate will use default
        if crf_enable == '1':
            sanity_check_evaluated_points = len(crf_list)
            for codec, rdoq_val, rdolevel_val, video, crf_val in product(codecs, params_dict['EnableRdoQuant'],
                                                                         params_dict['rdoLevel'],
                                                                         input_list, crf_list):
                # h264 encoder just support rdolevel=1, av1 doesnt support EnableRdoQuant
                if codec_map[codec] == 'h264' and rdolevel_val != '1':
                    continue
                if codec_map[codec] == 'av1' and rdoq_val == '1':
                    continue
                width, height, fps = re.findall(r'(\d+)x(\d+)_?p(\d+)', video)[0]
                xcoder_params = 'level=0:frameRate={}:RcEnable=0:vbvBufferSize=1000:crf={}:intraPeriod={}:gopPresetIdx=5:lookaheadDepth=10' \
                                ':cuLevelRCEnable=1:rdoLevel={}:EnableRdoQuant={}:bitrate=6000000:tolCtbRcIntra=0:tolCtbRcInter=0:zeroCopyMode=0' \
                    .format(fps, crf_val, 2 * int(fps), rdolevel_val, rdoq_val)

                extra_params = ''
                for param in params_dict:
                    if param not in params_limited:
                        if param in xcoder_params:
                            xcoder_params = re.sub(r'{}=\d*'.format(param),
                                                   '{}={}'.format(param, params_dict[param][0]),
                                                   xcoder_params)
                        else:
                            extra_params += '{}={}:'.format(param, params_dict[param][0])
                if extra_params:
                    xcoder_params += ':{}'.format(extra_params)

                frame_num, video_name = video.split('_')[-1], video.split('_')[:-1]
                # sub_path = video_path_map[width + 'x' + height] if width + 'x' + height != '3840x2160' else \
                # video_path_map[width + 'x' + height]['p{}'.format(fps)]
                video_path = '/home/<USER>/sources/'.format(user) + sub_path + '/' + "_".join(
                    video_name + [frame_num]) + '.yuv'
                group_name = 'crf{}_rdoq{}/rdolevel{}_{}'.format(crf_enable, rdoq_val, rdolevel_val, codec_map[codec])
                output_file = '/home/<USER>/XCODER/'.format(user) + '{}/{}_{}_rdoq{}_rdolevel{}_{}p{}_crf{}.{}' \
                    .format(group_name, cfg_file_name.split('.')[0], video, rdoq_val, rdolevel_val, codec_map[codec],
                            fps,
                            crf_val,
                            codec_map[codec] if codec_map[codec] != 'av1' else 'ivf')
                libvmaf_path = '{6}/{0}_{1}_rdoq{2}_rdolevel{3}_crf{4}_{5}_vmaf.json'.format(
                    cfg_file_name.split('.')[0],
                    video, rdoq_val, rdolevel_val,
                    crf_val, codec_map[codec],
                    group_name)
                psnr_path = '{6}/{0}_{1}_rdoq{2}_rdolevel{3}_crf{4}_{5}_psnr.txt'.format(cfg_file_name.split('.')[0],
                                                                                         video,
                                                                                         rdoq_val, rdolevel_val,
                                                                                         crf_val,
                                                                                         codec_map[codec], group_name)
                ssim_path = '{6}/{0}_{1}_rdoq{2}_rdolevel{3}_crf{4}_{5}_ssim.txt'.format(cfg_file_name.split('.')[0],
                                                                                         video,
                                                                                         rdoq_val,
                                                                                         rdolevel_val, crf_val,
                                                                                         codec_map[codec], group_name)
                analysis_group[group_name].append(
                    [output_file, libvmaf_path, psnr_path, ssim_path, frame_num, fps, "_".join(video_name)])
                sub_cmds.append(
                    base_cmd.format(width, height, fps, video_path, codec_map[codec], xcoder_params, frame_num,
                                    output_file))
                sub_quality_cmds.append(
                    quality_cmd.format(vidip_ffmpeg, fps, output_file, width, height, fps, video_path,
                                       vmaf_map[width + 'x' + height],
                                       user, libvmaf_path, user, psnr_path, user, ssim_path, frame_num))
                count += 1
                if count == max_instances:
                    CMDs.append(sub_cmds)
                    quality_CMDs.append(sub_quality_cmds)
                    sub_cmds = []
                    sub_quality_cmds = []
                    count = 0

        # when enable crf, bitrate will use default
        else:
            sanity_check_evaluated_points = len(bitrate_list)
            for codec, rdoq_val, rdolevel_val, video, bitrate in product(codecs, params_dict['EnableRdoQuant'],
                                                                         params_dict['rdoLevel'],
                                                                         input_list, bitrate_list):
                # h264 encoder just support rdolevel=1, av1 doesnt support EnableRdoQuant
                if codec_map[codec] == 'h264' and rdolevel_val != '1':
                    continue
                if codec_map[codec] == 'av1' and rdoq_val == '1':
                    continue
                width, height, fps = re.findall(r'(\d+)x(\d+)_?p(\d+)', video)[0]
                if int(fps) == 50:
                    bitrate = int(int(bitrate) / 1.2)
                elif int(fps) == 25:
                    bitrate = int(int(bitrate) / 2.4)
                if 'ippp' not in cfg_file_name:
                    xcoder_params = 'level=0:frameRate={}:RcEnable=1:vbvBufferSize=2000:bitrate={}:intraPeriod={}:gopPresetIdx=-1:entropyCodingMode=1:lookaheadDepth=16' \
                                    ':cuLevelRCEnable=0:rdoLevel={}:EnableRdoQuant={}'.format(fps, bitrate,
                                                                                              2 * int(fps),
                                                                                              rdolevel_val, rdoq_val)
                else:
                    xcoder_params = 'level=0:frameRate={}:RcEnable=1:vbvBufferSize=2000:bitrate={}:intraPeriod={}:gopPresetIdx=3:entropyCodingMode=1:lookaheadDepth=0' \
                                    ':cuLevelRCEnable=1:rdoLevel={}:EnableRdoQuant={}:tolCtbRcIntra=0:tolCtbRcInter=0' \
                        .format(fps, bitrate, 2 * int(fps), rdolevel_val, rdoq_val)

                extra_params = ''
                for param in params_dict:
                    if param not in params_limited:
                        if param in xcoder_params:
                            xcoder_params = re.sub(r'{}=\d*'.format(param),
                                                   '{}={}'.format(param, params_dict[param][0]),
                                                   xcoder_params)
                        else:
                            extra_params += '{}={}:'.format(param, params_dict[param][0])
                if extra_params:
                    xcoder_params += ':{}'.format(extra_params)

                frame_num, video_name = video.split('_')[-1], video.split('_')[:-1]
                # sub_path = video_path_map[width + 'x' + height] if width + 'x' + height != '3840x2160' else video_path_map[width + 'x' + height]['p{}'.format(fps)]
                video_path = '/home/<USER>/sources/'.format(user) + sub_path + '/' + "_".join(
                    video_name + [frame_num]) + '.yuv'
                group_name = 'crf{}_rdoq{}/rdolevel{}_{}'.format(crf_enable, rdoq_val, rdolevel_val, codec_map[codec])
                output_file = '/home/<USER>/XCODER/'.format(user) + '{}/{}_{}_rdoq{}_rdolevel{}_{}p{}_bitrate{}.{}' \
                    .format(group_name, cfg_file_name.split('.')[0], video, rdoq_val, rdolevel_val, codec_map[codec],
                            fps,
                            bitrate,
                            codec_map[codec] if codec_map[codec] != 'av1' else 'ivf')
                libvmaf_path = '{}/{}_{}_rdoq{}_rdolevel{}_bitrate{}_{}_vmaf.json'.format(group_name,
                                                                                          cfg_file_name.split('.')[0],
                                                                                          video,
                                                                                          rdoq_val, rdolevel_val,
                                                                                          bitrate,
                                                                                          codec_map[codec])
                psnr_path = '{}/{}_{}_rdoq{}_rdolevel{}_bitrate{}_{}_psnr.txt'.format(group_name,
                                                                                      cfg_file_name.split('.')[0],
                                                                                      video,
                                                                                      rdoq_val,
                                                                                      rdolevel_val, bitrate,
                                                                                      codec_map[codec])
                ssim_path = '{}/{}_{}_rdoq{}_rdolevel{}_bitrate{}_{}_ssim.txt'.format(group_name,
                                                                                      cfg_file_name.split('.')[0],
                                                                                      video,
                                                                                      rdoq_val,
                                                                                      rdolevel_val, bitrate,
                                                                                      codec_map[codec])
                analysis_group[group_name].append([output_file, libvmaf_path, psnr_path, ssim_path, frame_num, fps,
                                                   "_".join(video_name)])
                sub_cmds.append(
                    base_cmd.format(width, height, fps, video_path, codec_map[codec], xcoder_params, frame_num,
                                    output_file))
                sub_quality_cmds.append(
                    quality_cmd.format(vidip_ffmpeg, fps, output_file, width, height, fps, video_path,
                                       vmaf_map[width + 'x' + height], user, libvmaf_path, user,
                                       psnr_path, user, ssim_path, frame_num))
                count += 1
                if count == max_instances:
                    CMDs.append(sub_cmds)
                    quality_CMDs.append(sub_quality_cmds)
                    sub_cmds = []
                    sub_quality_cmds = []
                    count = 0
        if sub_cmds:
            CMDs.append(sub_cmds)
            quality_CMDs.append(sub_quality_cmds)
    return CMDs, quality_CMDs, analysis_group, sanity_check_evaluated_points


def gaudi_command_prepare(test_root, root_dir,codecs, input_list, bitrate_list, simulation_type, cfg_file_name, mode, param_dict, video_path_map, codec_map, vmaf_map, analysis_group,encoder_codec, video_dir, mctf_inputs, mctf_inputs_cqp):
    if not os.path.exists(f'{test_root}/gaudi_cfg'):
        os.makedirs(f'{test_root}/gaudi_cfg')
    if len(new_dir) > 0:
        for dirs in new_dir:
            os.makedirs(f'{root_dir}/{dirs}', exist_ok=True)
    quality_cmd = '{} -r {} -i {} -s:v {}x{} -r {} -i {} ' \
                  '-filter_complex "[0:v][1:v]libvmaf=model="path={}":log_path={}:log_fmt=json;' \
                  '[0:v][1:v]psnr=stats_file={};[0:v][1:v]ssim=stats_file={}" -vframes {} -f null -' + '\n'
    quality_CMDs = []
    if "/mnt/ceph" in video_dir:
        base_dir = '/mnt/ceph'
    else:
        base_dir = video_dir.replace('/sources', '').rstrip('/')
    app_name = '{}/Gaudi'.format(test_root)
    ffmpeg_name = '{}/ffmpeg'.format(test_root)
    commands = []
    rdolevel = encoder_codec

    for codec, video, bitrate in product(codecs, input_list, bitrate_list):
        codec_param_dict = {}
        codec_param = codec_map[codec].strip()
        if codec_param in codecs_params_dict and len(codecs_params_dict[codec_param]) > 0:
            for k, v in codecs_params_dict[codec_param].items():
                codec_param_dict[k] = v
        if len(codec_param_dict) > 0 and 'gop_type' in codec_param_dict:
            gop_type = int(codec_param_dict['gop_type'])
        elif 'gop_type' in param_dict:
            gop_type = int(param_dict['gop_type'])
        else:
            if 'bframes' in mode and codec == '1':
                gop_type = 8
            elif 'bframes' in mode and codec == '0':
                gop_type = 5
            elif 'bframes' in mode and codec == '2':
                gop_type = 8
            else:
                gop_type = 2

        enc_type = 1
        if codec == '2':
            enc_type = 27
        if codec == '1':
            enc_type = 1
        if codec == '0':
            enc_type = 3
        width, height, fps = re.findall(r'(\d+)x(\d+)_?p(\d+)', video)[0]
        sub_path = video_path_map[width + 'x' + height] if width + 'x' + height != '3840x2160' else \
            video_path_map[width + 'x' + height]['p{}'.format(fps)]
        if "cqp" not in simulation_type:
            if int(fps) == 50:
                bitrate = int(int(bitrate))
            elif int(fps) == 25:
                bitrate = int(int(bitrate))
        intra_period = int(codec_param_dict.get('intra_period', param_dict.get('intra_period', 2)))
        bit_depth = str(codec_param_dict.get('bit_depth', param_dict.get('bit_depth',8)))

        ext = codec_map[codec] if codec_map[codec] != 'av1' else 'ivf'

        cfg_name = f'{test_root}/gaudi_cfg' + '/' + cfg_file_name.split('.')[0] + '_' + video + \
                   f'_{simulation_type}_' + str(bitrate) + f'_{codec_map[codec]}' + '_gop_' + str(gop_type) + '.cfg'
        output = '{}/{}/'.format(test_root,codec_map[codec]) + cfg_file_name.split('.')[
            0] + '_' + video + \
                 '_br_' + str(bitrate) + '_gop_' + str(gop_type) + '.' + ext

        frame_num, video_name = video.split('_')[-1], video.split('_')[:-1]
        input_video_path = '{}/sources/{}/{}_420.yuv'.format(base_dir, sub_path, "_".join(video_name))
        libvmaf_path = '{}/{}/'.format(test_root, codec_map[codec]) + cfg_file_name.split('.')[
            0] + '_' + video + '_br_' + str(bitrate) + '_gop_' + str(gop_type) + '_vmaf.json'
        psnr_path = '{}/{}/'.format(test_root, codec_map[codec]) + cfg_file_name.split('.')[
            0] + '_' + video + '_br_' + str(bitrate) + '_gop_' + str(gop_type) + '_psnr.json'
        ssim_path = '{}/{}/'.format(test_root, codec_map[codec]) + cfg_file_name.split('.')[
            0] + '_' + video + '_br_' + str(bitrate) + '_gop_' + str(gop_type) + '_ssim.json'
        quality_CMDs.append(quality_cmd.format(ffmpeg_name, fps, output, width, height, fps, input_video_path,
                                               vmaf_map[width + 'x' + height], libvmaf_path, psnr_path, ssim_path, frame_num))
        analysis_group[rdolevel][codec_map[codec]].append(
            [output, libvmaf_path, psnr_path, ssim_path, frame_num, fps, "_".join(video_name)])

        twoPassDumps = f'{root_dir}/SatdFile/{"_".join(video_name)}_QP_{codec_map[codec]}_2PASS.txt'.replace("1920x1080", "960x544")
        twoPassstats = f'{root_dir}/SatdFile/{"_".join(video_name)}_QP_{codec_map[codec]}_2PASS.log'.replace("1920x1080", "960x544")
        dqp_file = f'{root_dir}/dqp_files/{"_".join(video_name)}_QP_{codec_map[codec]}.dqp'.replace("1920x1080", "960x544")
        agop_stat_file = f'{root_dir}/AdaptiveGopStatFile/{"_".join(video_name)}_QP_{codec_map[codec]}.stats'.replace("1920x1080", "960x544")
        avg_dqp_file = f'{root_dir}/avg_dqp_files/{"_".join(video_name)}_QP_{codec_map[codec]}.stats'.replace("1920x1080", "960x544")
        if codec_map[codec] == 'av1':
            mctf_path = f'{root_dir}/MCTF_YUV/AV1/'
        else:
            mctf_path = f'{root_dir}/MCTF_YUV/HEVC/'
        #mctf_input_path = '{}/sources/{}/{}_420_MCTF_{}_{}.yuv'.format(base_dir, sub_path, "_".join(video_name),codec_map[codec].upper(),bitrate)
        mctf_yuv_path = mctf_path + f'{"_".join(video_name)}_QP.yuv'
        if mctf_inputs:
            mctf_yuv_path = mctf_path + f'{"_".join(video_name)}_{bitrate}.yuv'
        cmd_dict = {
            'RateControl': 2,
            'VbvBufferSize': 2000
        }

        for k in cmd_dict.keys():
            if k in param_dict:
                cmd_dict[k] = param_dict[k]
            cmd_dict[k] = str(cmd_dict[k])

        extraAddedParams = ''
        for k, v in param_dict.items():
            if k not in cmd_dict:
                if v == '""':
                    if k == 'CrfSaveSatdFile':
                        v = twoPassDumps
                    elif k == 'DisplayOrderFile':
                        v = mctf_yuv_path
                    elif k == 'DqpFile':
                        v = dqp_file
                    elif k == 'AvgDqpFile':
                        v = avg_dqp_file
                    elif k == 'CustomMapFile':
                        v = dqp_file
                    elif k == 'AdaptGopReadFile':
                        v = agop_stat_file
                    elif k == 'AdaptGopStatFile':
                        v = agop_stat_file
                    elif k == 'TwoPassStatsFile':
                        v = twoPassstats
                    extraAddedParams += k + ' : ' + v + '\n'
                else:
                    if k not in ['gop_type', 'bit_depth', 'intra_period']:
                        extraAddedParams += k + ' : ' + v + '\n'
        if len(codec_param_dict) > 0:
            for k, v in codec_param_dict.items():
                if k not in ['gop_type', 'bit_depth', 'intra_period']:
                    extraAddedParams += k + ' : ' + v + '\n'
        input_video_path = '{}/sources/{}/{}_420.yuv'.format(base_dir, sub_path, "_".join(video_name)) if sub_path != 'cce_scaler' else \
                            '{}/sources/{}/{}_400.yuv'.format(base_dir, sub_path, "_".join(video_name))
        if mctf_inputs or mctf_inputs_cqp:
            input_video_path = mctf_yuv_path
        file = open(cfg_name, "w")
        file.write('InputFile : ' + input_video_path + '\n')
        file.write('SourceWidth : ' + str(width) + '\n')
        file.write('SourceHeight : ' + str(height) + '\n')
        file.write('FramesToBeEncoded : ' + str(frame_num) + '\n')
        file.write('GopPreset : ' + str(gop_type) + '\n')
        if 'cbr' in simulation_type:
            file.write('RateControl : ' + cmd_dict['RateControl'] + '\n')
            file.write('VbvBufferSize : ' + cmd_dict['VbvBufferSize'] + '\n')
        else:
            file.write('RateControl : 0' + '\n')
        if(ext == 'h264'):
             file.write( 'IdrPeriod : '+ str(intra_period * int(fps)) + '\n' )
        else:
            file.write('IntraPeriod : ' + str(intra_period * int(fps)) + '\n')
        file.write('FrameRate : ' + str(int(fps)) + '\n')
        file.write('InputBitDepth : ' + str(bit_depth) + '\n')
        if 'crf' in simulation_type:
            file.write('QP : 10' + '\n')
            file.write('CrfQpInt : ' + str(bitrate) + '\n')
        file.write(extraAddedParams + '\n')
        file.close()

        cmd = app_name + ' -i ' + cfg_name + ' -o ' + output
        if 'cbr' in simulation_type:
            cmd += ' -E ' + str(bitrate) + ' --codec_std ' + str(int(enc_type)) + '\n'
        else:
            cmd += ' --codec_std ' + str(int(enc_type)) + '\n'


        commands.append(cmd)

    commands = ''.join(commands)
    quality_CMDs = ''.join(quality_CMDs)

    # find and replace to make cmd array
    # remove waits
    commands = re.sub(r'wait \n', r'', commands)
    quality_CMDs = re.sub(r'wait \n', r'', quality_CMDs)
    # remove extra newlines
    commands = re.sub(r'(\n)+', r'\n', commands)
    quality_CMDs = re.sub(r'(\n)+', r'\n', quality_CMDs)
    # remove trailing &
    commands = re.sub(r'(.*) &', r'\1', commands)
    quality_CMDs = re.sub(r'(.*) &', r'\1', quality_CMDs)
    # add quote, indent
    commands = re.sub(r'(.*)\n', r"    '\1'\n", commands)
    quality_CMDs = re.sub(r'(.*)\n', r"    '\1'\n", quality_CMDs)

    count = commands.count('\n') - 1
    quality_count = quality_CMDs.count('\n') - 1

    header = f"""#!/bin/bash
#SBATCH --job-name=job_name
#SBATCH --array=0-{count}
#SBATCH --output=log/job_name_%A_%a.log
cmd=(
"""

    footer = """)

printf \"\\n Running: ${cmd[$SLURM_ARRAY_TASK_ID]} \\n\\n\"
eval \"${cmd[$SLURM_ARRAY_TASK_ID]}\""""

    full = header + commands + footer
    new = open(f"{test_root}/all.sh", "w")
    new.seek(0)
    new.truncate()
    new.write(full)
    new.close()

    header_quality = f"""#!/bin/bash
#SBATCH --job-name=job_name_analysis
#SBATCH --array=0-{quality_count}
#SBATCH --output=log/job_name_analysis_%A_%a.log
cmd=(
"""
    full = header_quality + quality_CMDs + footer

    new = open(f"{test_root}/all_analysis.sh", "w")
    new.seek(0)
    new.truncate()
    new.write(full)
    new.close()


############### Run FFmpeg Command function ###############
def exec_popen_cmd(cmd_list):
    p_info = []
    for cmd in cmd_list:
        print(cmd)
        p_info.append(Popen(cmd, shell=True, stdout=PIPE, stderr=STDOUT, universal_newlines=True))

    completions = len(p_info)
    for x in range(3000):
        if completions == 0:
            # self.logger.info('All Commands have Finished Running')
            break
        time.sleep(0.2)

        for j in range(len(p_info)):
            if isinstance(p_info[j], int):
                continue
            status = p_info[j].poll()
            if status != None:
                print(status)
                p_info[j] = status
                completions -= 1
                continue
            if x + 1 == 3000:
                print('\n Command {} Timeout (FAILURE)'.format(j))
                getoutput('sudo pkill --signal SIGTERM -P {}'.format(p_info[j].pid))
                p_info[j].poll()
                p_info[j] = 'Timeout'


############### Parse quality .txt file ###############
def read_psnr_temp_file(filename, total_frames_encoded):
    data_y = []
    data_u = []
    data_v = []
    with open(filename, "r") as in_psnr:
        # n:1 mse_avg:529.52 mse_y:887.00 mse_u:233.33 mse_v:468.25 psnr_avg:20.89 psnr_y:18.65 psnr_u:24.45 psnr_v:21.43
        lines = in_psnr.readlines()
        for i, line in enumerate(lines):
            if i >= total_frames_encoded:
                break
            line = line.strip()
            fields = line.split(" ")
            frame_data = {}
            for field in fields:
                k, v = field.split(":")
                frame_data[k] = round(float(v), 3) if k != "n" else int(v)
            data_y.append(frame_data["mse_y"])
            data_u.append(frame_data["mse_u"])
            data_v.append(frame_data["mse_v"])

    return data_y, data_u, data_v


def read_ssim_temp_file(filename, total_frames_encoded):
    data_y = []
    data_u = []
    data_v = []
    with open(filename, "r") as in_ssim:
        # n:1 Y:0.937213 U:0.961733 V:0.945788 All:0.948245 (12.860441)\n
        lines = in_ssim.readlines()
        for i, line in enumerate(lines):
            if i >= total_frames_encoded:
                break
            line = line.strip().split(" (")[0]  # remove excess
            fields = line.split(" ")
            frame_data = {}
            for field in fields:
                k, v = field.split(":")
                if k != "n":
                    # make psnr and ssim keys the same
                    k = "ssim_" + k.lower()
                    k = k.replace("all", "avg")
                frame_data[k] = round(float(v), 3) if k != "n" else int(v)
            data_y.append(frame_data["ssim_y"])
            data_u.append(frame_data["ssim_u"])
            data_v.append(frame_data["ssim_v"])

    return data_y, data_u, data_v


def read_vmaf_file(filename):
    file = open(filename)
    data_frame = json.load(file)
    data = []

    frames = data_frame['frames']
    for frame in frames:
        # print(frame['pkt_size'])
        # data.append(frame['pict_type'])
        data.append(frame['pkt_size'])

    return data


def average(lst):
    return sum(lst) / len(lst)


def parse_info(summary_file, test_root, compressed_name, vmaf_file, psnr_file, ssim_file, total_frames_encoded,
               frame_rate, video_input):
    check_attempts = 3
    for attempt in range(check_attempts):
        current_size = os.path.getsize(compressed_name)
    file_size = current_size
    bitrate = file_size * 8.0 * int(frame_rate) / float(total_frames_encoded)

    log_file_name_vmaf = vmaf_file
    log_file_name_psnr = psnr_file
    log_file_name_ssim = ssim_file

    file = open(log_file_name_vmaf)
    data_frame = json.load(file)
    vmaf = data_frame['pooled_metrics']['vmaf']['mean']

    mse_data_y, mse_data_u, mse_data_v = \
        read_psnr_temp_file(log_file_name_psnr, int(total_frames_encoded))

    ssim_data_y, ssim_data_u, ssim_data_v = \
        read_ssim_temp_file(log_file_name_ssim, int(total_frames_encoded))

    mse_y = average(mse_data_y)
    mse_u = average(mse_data_u)
    mse_v = average(mse_data_v)

    ssim_y = average(ssim_data_y)
    ssim_u = average(ssim_data_u)
    ssim_v = average(ssim_data_v)

    summary_file.write(compressed_name.split('/')[-1] + ',' + str(bitrate) + ',' +
                       str(mse_y) + ',' + str(mse_u) + ',' + str(mse_v) +
                       ',' + str(ssim_y) + ',' + str(ssim_u) + ',' +
                       str(ssim_v) + ',' + str(vmaf) +
                       ',' + str(total_frames_encoded) + '\n')


def argsort(seq):
    return sorted(range(len(seq)), key=seq.__getitem__, reverse=True)


############### Read quality .txt Compared with Anchor ###############
def read_data(filename, prefix, sanity_check_evaluated_points):
    data_points_limit = sanity_check_evaluated_points

    # new implementation : all array sizes are dynamically set based on specified data limit
    bit_rate = [-100] * sanity_check_evaluated_points
    psnr_y = [-100] * sanity_check_evaluated_points
    psnr_u = [-100] * sanity_check_evaluated_points
    psnr_v = [-100] * sanity_check_evaluated_points
    ssim_y = [-100] * sanity_check_evaluated_points
    ssim_u = [-100] * sanity_check_evaluated_points
    ssim_v = [-100] * sanity_check_evaluated_points
    vmaf = [-100] * sanity_check_evaluated_points
    frames_encoded = [-100] * sanity_check_evaluated_points

    if (os.path.exists(filename)):
        count = 0
        with open(filename, "r") as input_file:

            lines = input_file.readlines()
            for line in lines:
                if prefix in line and count < data_points_limit:
                    line = line.strip()
                    line = re.sub(" +", " ", line)
                    fields = line.split(",")
                    bit_rate[count] = float(fields[1]) / 1000

                    v = float(fields[2])
                    if (math.isinf(v) or math.isnan(v)):
                        v = 99
                    psnr_y[count] = v
                    v = float(fields[3])
                    if (math.isinf(v) or math.isnan(v)):
                        v = 99
                    psnr_u[count] = v
                    v = float(fields[4])
                    if (math.isinf(v) or math.isnan(v)):
                        v = 99
                    psnr_v[count] = v
                    v = float(fields[5])
                    if (math.isinf(v) or math.isnan(v)):
                        v = 1
                    ssim_y[count] = v
                    v = float(fields[6])
                    if (math.isinf(v) or math.isnan(v)):
                        v = 1
                    ssim_u[count] = v
                    v = float(fields[7])
                    if (math.isinf(v) or math.isnan(v)):
                        v = 1
                    ssim_v[count] = v
                    v = float(fields[8])
                    if (math.isinf(v) or math.isnan(v)):
                        v = 100
                    vmaf[count] = v
                    v = float(fields[9])
                    if (math.isinf(v) or math.isnan(v)):
                        v = 120
                    frames_encoded[count] = v
                    count = count + 1
                else:
                    if (count > data_points_limit):
                        print("reading more points than expected from summary")
                        print("check results file")
                        print(filename)

        order = argsort(bit_rate)

        bit_rate = [bit_rate[i] for i in order]
        psnr_y = [psnr_y[i] for i in order]
        psnr_u = [psnr_u[i] for i in order]
        psnr_v = [psnr_v[i] for i in order]
        ssim_y = [ssim_y[i] for i in order]
        ssim_u = [ssim_u[i] for i in order]
        ssim_v = [ssim_v[i] for i in order]
        vmaf = [vmaf[i] for i in order]
    return GraphData(bit_rate, psnr_y, psnr_u, psnr_v, ssim_y, ssim_u, ssim_v,
                     vmaf)


def add_entry(simulation_results, main_group, sim_name, value):
    # to add bd_range again, use this format:
    # config.simulation_results[rm][sq][en][rn][bdr] = {}
    # print("rm_sq_en: " , rm_sq_en)
    print(f"Debug: Storing data with key '{sim_name}' in group '{main_group}'")
    simulation_results[main_group][sim_name] = {}
    simulation_results[main_group][sim_name]['psnr_y'] = value[0]
    simulation_results[main_group][sim_name]['psnr_u'] = value[1]
    simulation_results[main_group][sim_name]['psnr_v'] = value[2]
    simulation_results[main_group][sim_name]['ssim_y'] = value[3]
    simulation_results[main_group][sim_name]['ssim_u'] = value[4]
    simulation_results[main_group][sim_name]['ssim_v'] = value[5]
    simulation_results[main_group][sim_name]['vmaf'] = value[6]
    simulation_results[main_group][sim_name]['apsnr'] = value[7]
    simulation_results[main_group][sim_name]['assim'] = value[8]


def add_entry_raw_data(simulation_graphs, main_group, sim_name, data, ref):
    simulation_graphs[main_group][sim_name] = {}
    simulation_graphs[main_group][sim_name]['br_seq'] = data.bitrate
    simulation_graphs[main_group][sim_name]['psnr_y_seq'] = data.psnr_y
    simulation_graphs[main_group][sim_name]['psnr_u_seq'] = data.psnr_u
    simulation_graphs[main_group][sim_name]['psnr_v_seq'] = data.psnr_v
    simulation_graphs[main_group][sim_name]['ssim_y_seq'] = data.ssim_y
    simulation_graphs[main_group][sim_name]['ssim_u_seq'] = data.ssim_u
    simulation_graphs[main_group][sim_name]['ssim_v_seq'] = data.ssim_v
    simulation_graphs[main_group][sim_name]['vmaf_seq'] = data.vmaf
    simulation_graphs[main_group][sim_name]['br_ref'] = ref.bitrate
    simulation_graphs[main_group][sim_name]['psnr_y_ref'] = ref.psnr_y
    simulation_graphs[main_group][sim_name]['psnr_u_ref'] = ref.psnr_u
    simulation_graphs[main_group][sim_name]['psnr_v_ref'] = ref.psnr_v
    simulation_graphs[main_group][sim_name]['ssim_y_ref'] = ref.ssim_y
    simulation_graphs[main_group][sim_name]['ssim_u_ref'] = ref.ssim_u
    simulation_graphs[main_group][sim_name]['ssim_v_ref'] = ref.ssim_v
    simulation_graphs[main_group][sim_name]['vmaf_ref'] = ref.vmaf


def bdrate2(metric_set1, metric_set2):
    """
    BJONTEGAARD    Bjontegaard metric calculation adapted
    Bjontegaard's metric allows to compute the average % saving in bitrate
    between two rate-distortion curves [1].  This is an adaptation of that
    method that fixes inconsistencies when the curve fit operation goes awry
    by replacing the curve fit function with a Piecewise Cubic Hermite
    Interpolating Polynomial and then integrating that by evaluating that
    function at small intervals using the trapezoid method to calculate
    the integral.
    metric_set1 - list of tuples ( bitrate,  metric ) for first graph
    metric_set2 - list of tuples ( bitrate,  metric ) for second graph
    """

    if not metric_set1 or not metric_set2:
        return 0.0

    try:
        # pchip_interlopate requires keys sorted by x axis. x-axis will
        # be our metric not the bitrate so sort by metric.
        metric_set1.sort(key=lambda tup: tup[1])
        metric_set2.sort(key=lambda tup: tup[1])
        # Pull the log of the rate and clamped psnr from metric_sets.
        log_rate1 = [math.log(x[0]) for x in metric_set1]
        metric1 = [100.0 if x[1] == float('inf') else x[1] for x in metric_set1]
        log_rate2 = [math.log(x[0]) for x in metric_set2]
        metric2 = [100.0 if x[1] == float('inf') else x[1] for x in metric_set2]
        # Integration interval.  This metric only works on the area that's
        # overlapping.   Extrapolation of these things is sketchy so we avoid.
        min_int = max([min(metric1), min(metric2)])
        max_int = min([max(metric1), max(metric2)])
        # No overlap means no sensible metric possible.
        if max_int <= min_int:
            return 0.0
        # Use Piecewise Cubic Hermite Interpolating Polynomial interpolation to
        # create 100 new samples points separated by interval.
        lin = np.linspace(min_int, max_int, num=100, retstep=True)
        interval = lin[1]
        samples = lin[0]
        v1 = scipy.interpolate.pchip_interpolate(metric1, log_rate1, samples)
        v2 = scipy.interpolate.pchip_interpolate(metric2, log_rate2, samples)
        # Calculate the integral using the trapezoid method on the samples.
        int_v1 = np.trapz(v1, dx=interval)
        int_v2 = np.trapz(v2, dx=interval)
        # Calculate the average improvement.
        avg_exp_diff = (int_v2 - int_v1) / (max_int - min_int)
    except (TypeError, ZeroDivisionError, ValueError, np.RankWarning) as e:
        return 0

    # Convert to a percentage.
    avg_diff = (math.exp(avg_exp_diff) - 1) * 100

    return avg_diff


def bd_rate_calc(data_rate, data_metric, ref_rate, ref_metric, sel_range):
    ref_rate = [ref_rate[i] for i in sel_range]
    ref_metric = [ref_metric[i] for i in sel_range]

    data_rate = [data_rate[i] for i in sel_range]
    data_metric = [data_metric[i] for i in sel_range]

    for i in range(1, len(data_metric)):
        if abs(data_metric[i] - data_metric[i - 1]) < 0.001:
            data_metric[i] = data_metric[i] + 0.000001

    for i in range(1, len(ref_metric)):
        if abs(ref_metric[i] - ref_metric[i - 1]) < 0.001:
            ref_metric[i] = ref_metric[i] + 0.000001

    for i in range(1, len(data_rate)):
        if abs(data_rate[i] - data_rate[i - 1]) < 0.001:
            data_rate[i] = data_rate[i] + 0.01

    for i in range(1, len(ref_rate)):
        if abs(ref_rate[i] - ref_rate[i - 1]) < 0.001:
            ref_rate[i] = ref_rate[i] + 0.01

    data_tuples = list(zip(data_rate, data_metric))
    ref_tuples = list(zip(ref_rate, ref_metric))

    bd_rate = bdrate2(ref_tuples, data_tuples)

    return bd_rate


def compute_psnr(mse):
    if (mse <= 0.0 or math.isnan(mse) or
            math.isinf(mse)):
        mse = 0.001

    psnr = -10.0 * math.log10(mse / (255.0 * 255.0))

    return psnr


def compute_psnr_vec(mse):
    psnr = [compute_psnr(mse_val) for mse_val in mse]
    return psnr


def compute_weighted_psnr(mse_y, mse_u, mse_v):
    weighted_psnr = []

    for i, y in enumerate(mse_y):
        u = mse_u[i]
        v = mse_v[i]
        weighted_mse = (4.0 * y + u + v) / 6.0
        wpsnr = compute_psnr(weighted_mse)
        weighted_psnr.append(wpsnr)

    return weighted_psnr


def compute_weighted_ssim(ssim_y, ssim_u, ssim_v):
    weighted_ssim = []

    for i, y in enumerate(ssim_y):
        u = ssim_u[i]
        v = ssim_v[i]
        w_ssim = (4.0 * y + u + v) / 6.0
        weighted_ssim.append(w_ssim)

    return weighted_ssim


def bd_rate_compute(data, ref, sel_range, sanity_check_evaluated_points):
    elem = [-201, -202, -203, -204, -205, -206, -207, -208, -209]
    if (len(data.bitrate) != sanity_check_evaluated_points or
            len(ref.bitrate) != sanity_check_evaluated_points):
        print('input length error', len(data.bitrate), len(ref.bitrate))
        return elem
    data_weighted_psnr = compute_weighted_psnr(data.psnr_y, data.psnr_u, data.psnr_v)
    data_psnr_y = compute_psnr_vec(data.psnr_y)
    data_psnr_u = compute_psnr_vec(data.psnr_u)
    data_psnr_v = compute_psnr_vec(data.psnr_v)
    ref_weighted_psnr = compute_weighted_psnr(ref.psnr_y, ref.psnr_u, ref.psnr_v)
    ref_psnr_y = compute_psnr_vec(ref.psnr_y)
    ref_psnr_u = compute_psnr_vec(ref.psnr_u)
    ref_psnr_v = compute_psnr_vec(ref.psnr_v)

    data_weighted_ssim = compute_weighted_ssim(data.ssim_y, data.ssim_u, data.ssim_v)
    ref_weighted_ssim = compute_weighted_ssim(ref.ssim_y, ref.ssim_u, ref.ssim_v)

    bd_psnr_y = bd_rate_calc(data.bitrate, data_psnr_y, ref.bitrate,
                             ref_psnr_y, sel_range)

    bd_psnr_u = bd_rate_calc(data.bitrate, data_psnr_u, ref.bitrate,
                             ref_psnr_u, sel_range)
    bd_psnr_v = bd_rate_calc(data.bitrate, data_psnr_v, ref.bitrate,
                             ref_psnr_v, sel_range)

    bd_ssim_y = bd_rate_calc(data.bitrate, data.ssim_y, ref.bitrate,
                             ref.ssim_y, sel_range)

    bd_ssim_u = bd_rate_calc(data.bitrate, data.ssim_u, ref.bitrate,
                             ref.ssim_u, sel_range)

    bd_ssim_v = bd_rate_calc(data.bitrate, data.ssim_v, ref.bitrate,
                             ref.ssim_v, sel_range)

    bd_vmaf = bd_rate_calc(data.bitrate, data.vmaf, ref.bitrate,
                           ref.vmaf, sel_range)

    bd_apsnr = bd_rate_calc(data.bitrate, data_weighted_psnr, ref.bitrate,
                            ref_weighted_psnr, sel_range)

    bd_assim = bd_rate_calc(data.bitrate, data_weighted_ssim, ref.bitrate,
                            ref_weighted_ssim, sel_range)
    if (math.isnan(bd_vmaf)):
        print("bd_vmaf nan")
        print("data ", data.bitrate, data.vmaf)
        print("ref ", ref.bitrate, ref.vmaf)
        bd_vmaf = 100
    elem = [bd_psnr_y, bd_psnr_u, bd_psnr_v, bd_ssim_y, bd_ssim_u, bd_ssim_v,
            bd_vmaf, bd_apsnr, bd_assim]

    return elem


def get_average(data):
    points = len(data)
    value_size = len(data[0])
    sums = np.zeros(value_size)
    averages = np.zeros(value_size)

    for d in data:
        idx = 0
        for el in d:
            sums[idx] = sums[idx] + el
            idx = idx + 1

    for i in range(0, value_size):
        averages[i] = sums[i] / points

    averages = averages.tolist()

    return averages


############### Create Excel Summary for result ###############
def write_val_to_worsheet(worksheet, row, col, val, f_pos, f_neg, cell_format):
    worksheet.write(row, col, val, cell_format)
    worksheet.conditional_format(row, col, row, col, f_pos)
    worksheet.conditional_format(row, col, row, col, f_neg)


def generate_summary_worksheet(simulation_results, worksheet, f_pos, f_neg, f_cell,
                               ref_type, main_group, rdolevel, reference_name, cfg_file_name):
    scale_name = cfg_file_name.split('.')[0]

    reference_x264 = 'x264_' + reference_name
    h264_ref_set = [reference_x264]
    reference_x265 = 'x265_' + reference_name
    h265_ref_set = [reference_x265]
    ref_set = h264_ref_set

    if (ref_type == 'av1'):
        ref_set = h265_ref_set
    if (ref_type == 'h265'):
        ref_set = h265_ref_set

    # iterate over resolution_and_mode

    worksheet.set_column(0, 0, 30)
    worksheet.set_column(0, 1, 15)
    for i in range(2, 5):
        worksheet.set_column(0, i, 10)
    for i in range(6, 11):
        worksheet.set_column(0, i, 15)

    worksheet.write(1, 0, 'Encoder')
    worksheet.write(1, 1, 'Format')
    worksheet.write(1, 2, 'BDRateY')
    worksheet.write(1, 3, 'BDRateU')
    worksheet.write(1, 4, 'BDRateV')
    worksheet.write(1, 5, 'BDRateAll')
    worksheet.write(1, 6, 'BDRateSSIMY')
    worksheet.write(1, 7, 'BDRateSSIMU')
    worksheet.write(1, 8, 'BDRateSSIMV')
    worksheet.write(1, 9, 'BDRateSSIMAll')
    worksheet.write(1, 10, 'BDRateVMAF')

    r = 2

    for rdo in rdolevel:

        average_data = []

        avg_sequence_set = np.zeros(9)

        cnt = 0
        # Only use sequences ending with '_all' for summary (comprehensive BD-rate data)
        for seq in simulation_results[main_group][ref_type]:
            # Filter to only include '_all' sequences for summary
            if not seq.endswith('_all'):
                continue

            reference = ref_set[0]
            bd_data = [-301, -302, -303, -304, -305, -306, -307, -308, -309]
            if seq:
                print(f"Debug: Using sequence '{seq}' for summary worksheet")
                bd_data[0] = simulation_results[main_group][ref_type][seq]['psnr_y']
                bd_data[1] = simulation_results[main_group][ref_type][seq]['psnr_u']
                bd_data[2] = simulation_results[main_group][ref_type][seq]['psnr_v']
                bd_data[3] = simulation_results[main_group][ref_type][seq]['ssim_y']
                bd_data[4] = simulation_results[main_group][ref_type][seq]['ssim_u']
                bd_data[5] = simulation_results[main_group][ref_type][seq]['ssim_v']
                bd_data[6] = simulation_results[main_group][ref_type][seq]['vmaf']
                bd_data[7] = simulation_results[main_group][ref_type][seq]['apsnr']
                bd_data[8] = simulation_results[main_group][ref_type][seq]['assim']
                bd_data = np.array(bd_data)
                avg_sequence_set = avg_sequence_set + bd_data
                cnt = cnt + 1

        if (cnt != 0):
            avg_sequence_set = avg_sequence_set / cnt
        else:
            continue
        v = avg_sequence_set

        ##############################

        worksheet.write(r, 0, rdo)
        worksheet.write(r, 1, scale_name)

        for i in range(0, 9):

            if (math.isnan(v[i])):
                v[i] = 100
                print("avg nan", i, rdo, scale_name, seq, cnt)

        write_val_to_worsheet(worksheet, r, 2, v[0], f_pos, f_neg, f_cell)
        write_val_to_worsheet(worksheet, r, 3, v[1], f_pos, f_neg, f_cell)
        write_val_to_worsheet(worksheet, r, 4, v[2], f_pos, f_neg, f_cell)
        write_val_to_worsheet(worksheet, r, 5, v[7], f_pos, f_neg, f_cell)
        write_val_to_worsheet(worksheet, r, 6, v[3], f_pos, f_neg, f_cell)
        write_val_to_worsheet(worksheet, r, 7, v[4], f_pos, f_neg, f_cell)
        write_val_to_worsheet(worksheet, r, 8, v[5], f_pos, f_neg, f_cell)
        write_val_to_worsheet(worksheet, r, 9, v[8], f_pos, f_neg, f_cell)
        write_val_to_worsheet(worksheet, r, 10, v[6], f_pos, f_neg, f_cell)

        data = [v[0], v[1], v[2], v[7], v[3], v[4], v[5], v[8],
                v[6]]
        average_data.append(data)

        r = r + 1

        average_val = get_average(average_data)
        worksheet.write(r, 0, rdo)
        worksheet.write(r, 1, 'Average')
        write_val_to_worsheet(worksheet, r, 2, average_val[0], f_pos, f_neg, f_cell)
        write_val_to_worsheet(worksheet, r, 3, average_val[1], f_pos, f_neg, f_cell)
        write_val_to_worsheet(worksheet, r, 4, average_val[2], f_pos, f_neg, f_cell)
        write_val_to_worsheet(worksheet, r, 5, average_val[3], f_pos, f_neg, f_cell)
        write_val_to_worsheet(worksheet, r, 6, average_val[4], f_pos, f_neg, f_cell)
        write_val_to_worsheet(worksheet, r, 7, average_val[5], f_pos, f_neg, f_cell)
        write_val_to_worsheet(worksheet, r, 8, average_val[6], f_pos, f_neg, f_cell)
        write_val_to_worsheet(worksheet, r, 9, average_val[7], f_pos, f_neg, f_cell)
        write_val_to_worsheet(worksheet, r, 10, average_val[8], f_pos, f_neg, f_cell)

        r = r + 1


def generate_summary_worksheet_per_sequence(simulation_results, worksheet, f_pos, f_neg, f_cell,
                                            ref_type, main_group, rdolevel, reference_name, cfg_file_name, input_list):
    reference_x264 = 'x264_' + reference_name
    h264_ref_set = [reference_x264]
    reference_x265 = 'x265_' + reference_name
    h265_ref_set = [reference_x265]
    ref_set = h264_ref_set

    if (ref_type == 'av1'):
        ref_set = h265_ref_set
    if (ref_type == 'h265'):
        ref_set = h265_ref_set
    worksheet.write(0, 0, 'Sequence')
    worksheet.set_column(0, 0, 55)

    r = 1
    w = 4

    sum_apsnr = np.zeros(len(rdolevel))
    sum_assim = np.zeros(len(rdolevel))
    sum_vmaf = np.zeros(len(rdolevel))
    cnt = 0

    # Debug: Print available keys in simulation_results
    print(f"Debug: Available keys in simulation_results[{main_group}]:")
    if main_group in simulation_results:
        for group_key in simulation_results[main_group].keys():
            print(f"  Group: {group_key}")
            if ref_type == group_key:  # Only show details for the current ref_type
                for seq_key in simulation_results[main_group][group_key].keys():
                    print(f"    - {seq_key}")
    else:
        print(f"  No data found for main_group={main_group}")

    print(f"Debug: Looking for sequences ending with '_all' in ref_type='{ref_type}'")

    for seq in input_list:
        # Process sequence name to match stored format
        seq_processed = '_'.join(seq.split('_')[:-1])
        worksheet.write(r, 0, seq_processed)
        worksheet.set_column(r, 0, 55)
        c = 1

        # Track if any data was found for this sequence
        sequence_data_found = False

        for idx, encoder in enumerate(rdolevel):
            bd_data = [-301, -302, -303, -304, -305, -306, -307, -308, -309]

            # Try multiple possible key formats to find the data
            possible_keys = [
                ref_type + '_' + seq_processed,      # e.g., "h264_BasketballDrill_832x480p50"
                ref_type + '_' + seq,                # e.g., "h264_BasketballDrill_832x480p50_8bit"
                ref_type + '_' + seq_processed + '_all',  # e.g., "h264_BasketballDrill_832x480p50_all"
                ref_type + '_' + seq + '_all',       # e.g., "h264_BasketballDrill_832x480p50_8bit_all"
                seq_processed,                       # Just the sequence name
                seq                                  # Original sequence name
            ]

            data_found = False
            for seq_name in possible_keys:
                # Check if data exists in the correct structure
                if (main_group in simulation_results and
                    seq_name in simulation_results[main_group]):

                    print(f"Debug: Found data for sequence '{seq_processed}' with key '{seq_name}'")

                    try:
                        bd_data[0] = simulation_results[main_group][seq_name]['psnr_y']
                        bd_data[1] = simulation_results[main_group][seq_name]['psnr_u']
                        bd_data[2] = simulation_results[main_group][seq_name]['psnr_v']
                        bd_data[3] = simulation_results[main_group][seq_name]['ssim_y']
                        bd_data[4] = simulation_results[main_group][seq_name]['ssim_u']
                        bd_data[5] = simulation_results[main_group][seq_name]['ssim_v']
                        bd_data[6] = simulation_results[main_group][seq_name]['vmaf']
                        bd_data[7] = simulation_results[main_group][seq_name]['apsnr']
                        bd_data[8] = simulation_results[main_group][seq_name]['assim']
                        bd_data = np.array(bd_data)
                        data_found = True
                        sequence_data_found = True
                        print(f"Debug: Successfully loaded data: {bd_data}")
                        break
                    except KeyError as e:
                        print(f"Debug: Key error accessing data for '{seq_name}': {e}")
                        continue

                # Also check if data exists under ref_type structure (alternative format)
                elif (main_group in simulation_results and
                      ref_type in simulation_results[main_group] and
                      seq_name in simulation_results[main_group][ref_type]):

                    print(f"Debug: Found data for sequence '{seq_processed}' with key '{seq_name}' under ref_type '{ref_type}'")

                    try:
                        bd_data[0] = simulation_results[main_group][ref_type][seq_name]['psnr_y']
                        bd_data[1] = simulation_results[main_group][ref_type][seq_name]['psnr_u']
                        bd_data[2] = simulation_results[main_group][ref_type][seq_name]['psnr_v']
                        bd_data[3] = simulation_results[main_group][ref_type][seq_name]['ssim_y']
                        bd_data[4] = simulation_results[main_group][ref_type][seq_name]['ssim_u']
                        bd_data[5] = simulation_results[main_group][ref_type][seq_name]['ssim_v']
                        bd_data[6] = simulation_results[main_group][ref_type][seq_name]['vmaf']
                        bd_data[7] = simulation_results[main_group][ref_type][seq_name]['apsnr']
                        bd_data[8] = simulation_results[main_group][ref_type][seq_name]['assim']
                        bd_data = np.array(bd_data)
                        data_found = True
                        sequence_data_found = True
                        print(f"Debug: Successfully loaded data: {bd_data}")
                        break
                    except KeyError as e:
                        print(f"Debug: Key error accessing data for '{seq_name}' under ref_type: {e}")
                        continue

            if not data_found:
                print(f"Warning: No data found for sequence '{seq_processed}' (original: '{seq}') in {ref_type}")
                # Keep default values (-301, -302, etc.) to indicate missing data
                bd_data = np.array(bd_data)

            # Write headers only once
            if (r == 1):
                worksheet.set_column(0, c, 16)
                worksheet.write(0, c, encoder)
                worksheet.write(0, c + 1, 'BD A PSNR')
                worksheet.write(0, c + 2, 'BD A SSIM')
                worksheet.write(0, c + 3, 'BD VMAF')

            # Write data regardless of whether it was found (to show missing data)
            worksheet.set_column(r, c + 1, 10)
            worksheet.set_column(r, c + 2, 10)
            worksheet.set_column(r, c + 3, 10)
            write_val_to_worsheet(worksheet, r, c + 1, bd_data[7], f_pos, f_neg, f_cell)
            write_val_to_worsheet(worksheet, r, c + 2, bd_data[8], f_pos, f_neg, f_cell)
            write_val_to_worsheet(worksheet, r, c + 3, bd_data[6], f_pos, f_neg, f_cell)

            # Only add to sums if valid data was found (not default negative values)
            if data_found and bd_data[7] > -300:
                sum_apsnr[idx] = sum_apsnr[idx] + bd_data[7]
                sum_assim[idx] = sum_assim[idx] + bd_data[8]
                sum_vmaf[idx] = sum_vmaf[idx] + bd_data[6]

            c = c + w

        # Only increment count if we found data for this sequence
        if sequence_data_found:
            cnt = cnt + 1
        r = r + 1

    # Write averages row
    if (cnt != 0):
        worksheet.write(r, 0, 'Average')
        c = 1
        for idx, encoder in enumerate(rdolevel):
            write_val_to_worsheet(worksheet, r, c + 1, sum_apsnr[idx] / cnt, f_pos, f_neg, f_cell)
            write_val_to_worsheet(worksheet, r, c + 2, sum_assim[idx] / cnt, f_pos, f_neg, f_cell)
            write_val_to_worsheet(worksheet, r, c + 3, sum_vmaf[idx] / cnt, f_pos, f_neg, f_cell)
            c = c + w
    else:
        print(f"Warning: No valid data found for any sequences in {ref_type} worksheet")


def generate_summary_worksheet_per_sequence_range(simulation_results, worksheet, f_pos, f_neg, f_cell,
                                                  ref_type, main_group, rdolevel, reference_name, cfg_file_name, input_list, range_suffix):
    """
    Generate sequence worksheet for specific range (low, medium, high, all)
    """
    reference_x264 = 'x264_' + reference_name
    h264_ref_set = [reference_x264]
    reference_x265 = 'x265_' + reference_name
    h265_ref_set = [reference_x265]
    ref_set = h264_ref_set

    if (ref_type == 'av1'):
        ref_set = h265_ref_set
    if (ref_type == 'h265'):
        ref_set = h265_ref_set

    worksheet.write(0, 0, f'Sequence ({range_suffix.upper()} Range)')
    worksheet.set_column(0, 0, 55)

    r = 1
    w = 4

    sum_apsnr = np.zeros(len(rdolevel))
    sum_assim = np.zeros(len(rdolevel))
    sum_vmaf = np.zeros(len(rdolevel))
    cnt = 0

    # Write headers
    c = 1
    for encoder in rdolevel:
        worksheet.write(r, c, encoder)
        worksheet.write(r + 1, c, 'APSNR')
        worksheet.write(r + 1, c + 1, 'ASSIM')
        worksheet.write(r + 1, c + 2, 'VMAF')
        worksheet.write(r + 1, c + 3, 'Count')
        c = c + w

    r = r + 2

    for seq in input_list:
        seq = '_'.join(seq.split('_')[:-1])
        worksheet.write(r, 0, seq)
        sequence_data_found = False

        c = 1
        for idx, encoder in enumerate(rdolevel):
            # Look for sequence data with the specific range suffix
            seq_name = ref_type + '_' + seq + '_' + range_suffix

            if seq_name in simulation_results[main_group][ref_type]:
                sequence_data_found = True
                bd_data = [-301, -302, -303, -304, -305, -306, -307, -308, -309]

                print(f"Debug: Found data for sequence '{seq_name}' in range '{range_suffix}'")
                bd_data[0] = simulation_results[main_group][ref_type][seq_name]['psnr_y']
                bd_data[1] = simulation_results[main_group][ref_type][seq_name]['psnr_u']
                bd_data[2] = simulation_results[main_group][ref_type][seq_name]['psnr_v']
                bd_data[3] = simulation_results[main_group][ref_type][seq_name]['ssim_y']
                bd_data[4] = simulation_results[main_group][ref_type][seq_name]['ssim_u']
                bd_data[5] = simulation_results[main_group][ref_type][seq_name]['ssim_v']
                bd_data[6] = simulation_results[main_group][ref_type][seq_name]['vmaf']
                bd_data[7] = simulation_results[main_group][ref_type][seq_name]['apsnr']
                bd_data[8] = simulation_results[main_group][ref_type][seq_name]['assim']

                write_val_to_worsheet(worksheet, r, c, bd_data[7], f_pos, f_neg, f_cell)
                write_val_to_worsheet(worksheet, r, c + 1, bd_data[8], f_pos, f_neg, f_cell)
                write_val_to_worsheet(worksheet, r, c + 2, bd_data[6], f_pos, f_neg, f_cell)
                worksheet.write(r, c + 3, 1)  # Count

                sum_apsnr[idx] += bd_data[7]
                sum_assim[idx] += bd_data[8]
                sum_vmaf[idx] += bd_data[6]
            else:
                print(f"Debug: No data found for sequence '{seq_name}' in range '{range_suffix}'")
                # Write empty cells
                worksheet.write(r, c, 'N/A')
                worksheet.write(r, c + 1, 'N/A')
                worksheet.write(r, c + 2, 'N/A')
                worksheet.write(r, c + 3, 0)

            c = c + w

        # Only increment count if we found data for this sequence
        if sequence_data_found:
            cnt = cnt + 1
        r = r + 1

    # Write averages row
    if (cnt != 0):
        worksheet.write(r, 0, f'Average ({range_suffix.upper()})')
        c = 1
        for idx, encoder in enumerate(rdolevel):
            write_val_to_worsheet(worksheet, r, c, sum_apsnr[idx] / cnt, f_pos, f_neg, f_cell)
            write_val_to_worsheet(worksheet, r, c + 1, sum_assim[idx] / cnt, f_pos, f_neg, f_cell)
            write_val_to_worsheet(worksheet, r, c + 2, sum_vmaf[idx] / cnt, f_pos, f_neg, f_cell)
            c = c + w
    else:
        print(f"Warning: No valid data found for any sequences in {ref_type} {range_suffix} range worksheet")


def generate_summary_worksheet_per_sequence_all_ranges(simulation_results, worksheet, f_pos, f_neg, f_cell,
                                                       ref_type, main_group, rdolevel, reference_name, cfg_file_name, input_list):
    """
    Generate comprehensive sequence worksheet showing ALL, Low, Medium, and High ranges in one sheet
    """
    reference_x264 = 'x264_' + reference_name
    h264_ref_set = [reference_x264]
    reference_x265 = 'x265_' + reference_name
    h265_ref_set = [reference_x265]
    ref_set = h264_ref_set

    if (ref_type == 'av1'):
        ref_set = h265_ref_set
    if (ref_type == 'h265'):
        ref_set = h265_ref_set

    worksheet.write(0, 0, 'Sequence Analysis - All Bitrate Ranges')
    worksheet.set_column(0, 0, 55)

    # Define ranges to process
    ranges = ['all', 'low', 'medium', 'high']
    range_labels = ['ALL', 'LOW', 'MEDIUM', 'HIGH']

    r = 1
    w = 16  # Width per encoder (4 ranges * 3 metrics + 3 gaps + 1 for encoder name)

    # Accumulators for averages
    sum_data = {}
    cnt_data = {}
    for range_name in ranges:
        sum_data[range_name] = {'apsnr': np.zeros(len(rdolevel)), 'assim': np.zeros(len(rdolevel)), 'vmaf': np.zeros(len(rdolevel))}
        cnt_data[range_name] = np.zeros(len(rdolevel))

    # Write main headers
    c = 1
    for encoder in rdolevel:
        worksheet.write(r, c, encoder)
        worksheet.write(r + 1, c, 'Range')
        worksheet.write(r + 1, c + 1, 'ALL')
        worksheet.write(r + 1, c + 5, 'LOW')      # Gap after ALL
        worksheet.write(r + 1, c + 9, 'MEDIUM')   # Gap after LOW
        worksheet.write(r + 1, c + 13, 'HIGH')   # Gap after MEDIUM

        # Sub-headers for metrics with gaps
        range_starts = [c + 1, c + 5, c + 9, c + 13]  # Starting positions with gaps
        for i, range_start in enumerate(range_starts):
            worksheet.write(r + 2, range_start, 'APSNR')
            worksheet.write(r + 2, range_start + 1, 'ASSIM')
            worksheet.write(r + 2, range_start + 2, 'VMAF')

        c = c + w

    r = r + 3

    # Process each sequence
    for seq in input_list:
        seq = '_'.join(seq.split('_')[:-1])
        worksheet.write(r, 0, seq)

        c = 1
        for idx, encoder in enumerate(rdolevel):
            # Process each range for this encoder
            range_starts = [c + 1, c + 5, c + 9, c + 13]  # Starting positions with gaps
            for range_idx, range_suffix in enumerate(ranges):
                seq_name = ref_type + '_' + seq + '_' + range_suffix

                range_col = range_starts[range_idx]  # Starting column for this range

                if seq_name in simulation_results[main_group][ref_type]:
                    print(f"Debug: Found data for sequence '{seq_name}' in range '{range_suffix}'")

                    bd_data = [-301, -302, -303, -304, -305, -306, -307, -308, -309]
                    bd_data[0] = simulation_results[main_group][ref_type][seq_name]['psnr_y']
                    bd_data[1] = simulation_results[main_group][ref_type][seq_name]['psnr_u']
                    bd_data[2] = simulation_results[main_group][ref_type][seq_name]['psnr_v']
                    bd_data[3] = simulation_results[main_group][ref_type][seq_name]['ssim_y']
                    bd_data[4] = simulation_results[main_group][ref_type][seq_name]['ssim_u']
                    bd_data[5] = simulation_results[main_group][ref_type][seq_name]['ssim_v']
                    bd_data[6] = simulation_results[main_group][ref_type][seq_name]['vmaf']
                    bd_data[7] = simulation_results[main_group][ref_type][seq_name]['apsnr']
                    bd_data[8] = simulation_results[main_group][ref_type][seq_name]['assim']

                    # Write the data
                    write_val_to_worsheet(worksheet, r, range_col, bd_data[7], f_pos, f_neg, f_cell)      # APSNR
                    write_val_to_worsheet(worksheet, r, range_col + 1, bd_data[8], f_pos, f_neg, f_cell)  # ASSIM
                    write_val_to_worsheet(worksheet, r, range_col + 2, bd_data[6], f_pos, f_neg, f_cell)  # VMAF

                    # Accumulate for averages
                    sum_data[range_suffix]['apsnr'][idx] += bd_data[7]
                    sum_data[range_suffix]['assim'][idx] += bd_data[8]
                    sum_data[range_suffix]['vmaf'][idx] += bd_data[6]
                    cnt_data[range_suffix][idx] += 1
                else:
                    print(f"Debug: No data found for sequence '{seq_name}' in range '{range_suffix}'")
                    # Write N/A for missing data
                    worksheet.write(r, range_col, 'N/A')
                    worksheet.write(r, range_col + 1, 'N/A')
                    worksheet.write(r, range_col + 2, 'N/A')

            c = c + w

        r = r + 1

    # Write averages row
    worksheet.write(r, 0, 'AVERAGE')
    c = 1
    for idx, encoder in enumerate(rdolevel):
        range_starts = [c + 1, c + 5, c + 9, c + 13]  # Starting positions with gaps
        for range_idx, range_suffix in enumerate(ranges):
            range_col = range_starts[range_idx]

            if cnt_data[range_suffix][idx] > 0:
                avg_apsnr = sum_data[range_suffix]['apsnr'][idx] / cnt_data[range_suffix][idx]
                avg_assim = sum_data[range_suffix]['assim'][idx] / cnt_data[range_suffix][idx]
                avg_vmaf = sum_data[range_suffix]['vmaf'][idx] / cnt_data[range_suffix][idx]

                write_val_to_worsheet(worksheet, r, range_col, avg_apsnr, f_pos, f_neg, f_cell)
                write_val_to_worsheet(worksheet, r, range_col + 1, avg_assim, f_pos, f_neg, f_cell)
                write_val_to_worsheet(worksheet, r, range_col + 2, avg_vmaf, f_pos, f_neg, f_cell)
            else:
                worksheet.write(r, range_col, 'N/A')
                worksheet.write(r, range_col + 1, 'N/A')
                worksheet.write(r, range_col + 2, 'N/A')

        c = c + w


def generate_detailed_worksheet(simulation_results, simulation_graphs, workbook, worksheet, f_pos, f_neg, f_cell,
                                ref_type, main_group, rdo, reference_name, cfg_file_name, rate_array, qp_array,
                                codecset, input_list,sanity_check_evaluated_points):
    bd_rate_header_format = workbook.add_format({'num_format': '0.00'})
    bd_rate_header_format.set_align('center')
    bd_rate_header_format.set_align('vcenter')
    bd_rate_header_format.set_text_wrap()

    scale_name = cfg_file_name.split('.')[0]

    reference_x264 = 'x264_' + reference_name
    h264_ref_set = [reference_x264]
    reference_x265 = 'x265_' + reference_name
    h265_ref_set = [reference_x265]
    ref_set = h264_ref_set
    if (ref_type == 'av1'):
        ref_set = h265_ref_set
    if (ref_type == 'h265'):
        ref_set = h265_ref_set
    codec = codecset

    # iterate over resolution_and_mode

    worksheet.write(0, 0, 'Anchor version')
    worksheet.write(0, 1, 'Test Version')
    worksheet.write(1, 0, 'Anchor Cmd')
    worksheet.write(1, 1, 'Test Cmd')

    worksheet.set_column('A:A', 15)
    worksheet.set_column('B:B', 30)
    worksheet.set_column('D:D', 12)
    worksheet.set_column('L:L', 12)

    worksheet.write(1, 7, 'Anchor')
    worksheet.write(1, 15, 'Test')

    worksheet.write(2, 0, 'Sequence Set')
    worksheet.write(2, 1, 'Sequence Name')
    worksheet.write(2, 2, 'QP\nRate (Kbps)', bd_rate_header_format)

    c = 3
    for i in range(0, 2):
        worksheet.write(2, c, 'Kbps', bd_rate_header_format)
        worksheet.write(2, c + 1, 'Y PSNR', bd_rate_header_format)
        worksheet.write(2, c + 2, 'U PSNR', bd_rate_header_format)
        worksheet.write(2, c + 3, 'V PSNR', bd_rate_header_format)
        worksheet.write(2, c + 4, 'Y SSIM', bd_rate_header_format)
        worksheet.write(2, c + 5, 'U SSIM', bd_rate_header_format)
        worksheet.write(2, c + 6, 'V SSIM', bd_rate_header_format)
        worksheet.write(2, c + 7, 'VMAF', bd_rate_header_format)
        c = c + 8

    worksheet.write(2, c, 'BD Rate\nPSNR YUV', bd_rate_header_format)
    worksheet.set_column('T:T', 10)
    worksheet.write(2, c + 1, 'BD Rate\nSSIM YUV', bd_rate_header_format)
    worksheet.set_column('U:U', 10)
    worksheet.write(2, c + 2, 'BD Rate\nVMAF', bd_rate_header_format)
    worksheet.set_column('V:V', 10)

    qp_rate = qp_array
    rate_sim = rate_array
    r = 3

    bd_set_summary = {}
    worksheet.write(r, 0, scale_name)

    n = sanity_check_evaluated_points
    sel_range_low = list(range(min(4, n)))
    sel_range_high = list(range(max(0, n-4), n))
    if n >= 6:
        start = math.ceil(n/2) - 2
        sel_range_medium = list(range(start, start+4))
    else:
        sel_range_medium = sel_range_high
    sel_range_all = list(range(n))

    for seq in input_list:
        # width = iv[1]
        # height = iv[2]
        # bit_depth = iv[4]
        seq = '_'.join(seq.split('_')[:-1])
        frame_rate = int(re.findall(r'p(\d+)', seq)[0])

        qp_rate = []
        # pixels = width * height
        if rate_array:
            for rate in rate_sim:
                if frame_rate == 25:
                    qp_rate.append(int(int(rate)))
                elif frame_rate == 50:
                    qp_rate.append(int(int(rate)))
                else:
                    qp_rate.append(int(rate))

            qp_rate = list(reversed(qp_rate))
        else:
            for rate in rate_sim:
                qp_rate.append(int(rate))

        bd_set_summary[seq] = {}

        worksheet.write(r, 1, seq)

        reference = ref_set[0]

        v = [[-401, -402, -403, -404, -405, -406, -407, -408],
             [-401, -402, -403, -404, -405, -406, -407, -408],
             [-401, -402, -403, -404, -405, -406, -407, -408],
             [-401, -402, -403, -404, -405, -406, -407, -408],
             [-401, -402, -403, -404, -405, -406, -407, -408],
             [-401, -402, -403, -404, -405, -406, -407, -408],
             [-401, -402, -403, -404, -405, -406, -407, -408],
             [-401, -402, -403, -404, -405, -406, -407, -408],
             [-401, -402, -403, -404, -405, -406, -407, -408],
             [-401, -402, -403, -404, -405, -406, -407, -408],
             [-401, -402, -403, -404, -405, -406, -407, -408],
             [-401, -402, -403, -404, -405, -406, -407, -408],
             [-401, -402, -403, -404, -405, -406, -407, -408],
             [-401, -402, -403, -404, -405, -406, -407, -408],
             [-401, -402, -403, -404, -405, -406, -407, -408],
             [-401, -402, -403, -404, -405, -406, -407, -408],
             ]

        seq_name = ref_type + '_' + seq

        v[0] = simulation_graphs[main_group][ref_type][seq_name]['br_seq']
        v[1] = simulation_graphs[main_group][ref_type][seq_name]['psnr_y_seq']
        v[2] = simulation_graphs[main_group][ref_type][seq_name]['psnr_u_seq']
        v[3] = simulation_graphs[main_group][ref_type][seq_name]['psnr_v_seq']
        v[4] = simulation_graphs[main_group][ref_type][seq_name]['ssim_y_seq']
        v[5] = simulation_graphs[main_group][ref_type][seq_name]['ssim_u_seq']
        v[6] = simulation_graphs[main_group][ref_type][seq_name]['ssim_v_seq']
        v[7] = simulation_graphs[main_group][ref_type][seq_name]['vmaf_seq']
        v[8] = simulation_graphs[main_group][ref_type][seq_name]['br_ref']
        v[9] = simulation_graphs[main_group][ref_type][seq_name]['psnr_y_ref']
        v[10] = simulation_graphs[main_group][ref_type][seq_name]['psnr_u_ref']
        v[11] = simulation_graphs[main_group][ref_type][seq_name]['psnr_v_ref']
        v[12] = simulation_graphs[main_group][ref_type][seq_name]['ssim_y_ref']
        v[13] = simulation_graphs[main_group][ref_type][seq_name]['ssim_u_ref']
        v[14] = simulation_graphs[main_group][ref_type][seq_name]['ssim_v_ref']
        v[15] = simulation_graphs[main_group][ref_type][seq_name]['vmaf_ref']

        v2 = [-301, -302, -303, -304, -305, -306, -307, -308, -309]
        v3 = [-301, -302, -303, -304, -305, -306, -307, -308, -309]
        v4 = [-301, -302, -303, -304, -305, -306, -307, -308, -309]
        v5 = [-301, -302, -303, -304, -305, -306, -307, -308, -309]

        # Assign metrics for all range (v2)
        v2[0] = simulation_results[main_group][ref_type][seq_name + '_all']['psnr_y']
        v2[1] = simulation_results[main_group][ref_type][seq_name + '_all']['psnr_u']
        v2[2] = simulation_results[main_group][ref_type][seq_name + '_all']['psnr_v']
        v2[3] = simulation_results[main_group][ref_type][seq_name + '_all']['ssim_y']
        v2[4] = simulation_results[main_group][ref_type][seq_name + '_all']['ssim_u']
        v2[5] = simulation_results[main_group][ref_type][seq_name + '_all']['ssim_v']
        v2[6] = simulation_results[main_group][ref_type][seq_name + '_all']['vmaf']
        v2[7] = simulation_results[main_group][ref_type][seq_name + '_all']['apsnr']
        v2[8] = simulation_results[main_group][ref_type][seq_name + '_all']['assim']

        # Assign metrics for low range (v3)
        v3[0] = simulation_results[main_group][ref_type][seq_name + '_low']['psnr_y']
        v3[1] = simulation_results[main_group][ref_type][seq_name + '_low']['psnr_u']
        v3[2] = simulation_results[main_group][ref_type][seq_name + '_low']['psnr_v']
        v3[3] = simulation_results[main_group][ref_type][seq_name + '_low']['ssim_y']
        v3[4] = simulation_results[main_group][ref_type][seq_name + '_low']['ssim_u']
        v3[5] = simulation_results[main_group][ref_type][seq_name + '_low']['ssim_v']
        v3[6] = simulation_results[main_group][ref_type][seq_name + '_low']['vmaf']
        v3[7] = simulation_results[main_group][ref_type][seq_name + '_low']['apsnr']
        v3[8] = simulation_results[main_group][ref_type][seq_name + '_low']['assim']

        # Assign metrics for medium range (v4)
        v4[0] = simulation_results[main_group][ref_type][seq_name + '_medium']['psnr_y']
        v4[1] = simulation_results[main_group][ref_type][seq_name + '_medium']['psnr_u']
        v4[2] = simulation_results[main_group][ref_type][seq_name + '_medium']['psnr_v']
        v4[3] = simulation_results[main_group][ref_type][seq_name + '_medium']['ssim_y']
        v4[4] = simulation_results[main_group][ref_type][seq_name + '_medium']['ssim_u']
        v4[5] = simulation_results[main_group][ref_type][seq_name + '_medium']['ssim_v']
        v4[6] = simulation_results[main_group][ref_type][seq_name + '_medium']['vmaf']
        v4[7] = simulation_results[main_group][ref_type][seq_name + '_medium']['apsnr']
        v4[8] = simulation_results[main_group][ref_type][seq_name + '_medium']['assim']

        # Assign metrics for high range (v5)
        v5[0] = simulation_results[main_group][ref_type][seq_name + '_high']['psnr_y']
        v5[1] = simulation_results[main_group][ref_type][seq_name + '_high']['psnr_u']
        v5[2] = simulation_results[main_group][ref_type][seq_name + '_high']['psnr_v']
        v5[3] = simulation_results[main_group][ref_type][seq_name + '_high']['ssim_y']
        v5[4] = simulation_results[main_group][ref_type][seq_name + '_high']['ssim_u']
        v5[5] = simulation_results[main_group][ref_type][seq_name + '_high']['ssim_v']
        v5[6] = simulation_results[main_group][ref_type][seq_name + '_high']['vmaf']
        v5[7] = simulation_results[main_group][ref_type][seq_name + '_high']['apsnr']
        v5[8] = simulation_results[main_group][ref_type][seq_name + '_high']['assim']

        # Convert to NumPy arrays
        v2 = np.array(v2)
        v3 = np.array(v3)
        v4 = np.array(v4)
        v5 = np.array(v5)

        if (math.isnan(v2[6])):
            v2[6] = 100
            print("all vmaf avg nan", rdo, scale_name, seq)

        if (math.isnan(v3[6])):
            v3[6] = 100
            print("low vmaf avg nan", rdo, scale_name, seq)

        if (math.isnan(v4[6])):
            v4[6] = 100
            print("medium vmaf avg nan", rdo, scale_name, seq)

        if math.isnan(v5[6]):
            v5[6] = 100
            print("high vmaf avg nan", rdo, scale_name, seq)

        avg_psnr_bd_all = v2[7]
        avg_ssim_bd_all = v2[8]
        vmaf_bd_all = v2[6]

        avg_psnr_bd_low = v3[7]
        avg_ssim_bd_low = v3[8]
        vmaf_bd_low = v3[6]

        avg_psnr_bd_medium = v4[7]
        avg_ssim_bd_medium = v4[8]
        vmaf_bd_medium = v4[6]

        avg_psnr_bd_high = v5[7]
        avg_ssim_bd_high = v5[8]
        vmaf_bd_high = v5[6]

        bd_set_summary[seq]['all'] = {}
        bd_set_summary[seq]['low'] = {}
        bd_set_summary[seq]['medium'] = {}
        bd_set_summary[seq]['high'] = {}

        bd_set_summary[seq]['all']['psnr'] = avg_psnr_bd_all
        bd_set_summary[seq]['all']['ssim'] = avg_ssim_bd_all
        bd_set_summary[seq]['all']['vmaf'] = vmaf_bd_all

        bd_set_summary[seq]['low']['psnr'] = avg_psnr_bd_low
        bd_set_summary[seq]['low']['ssim'] = avg_ssim_bd_low
        bd_set_summary[seq]['low']['vmaf'] = vmaf_bd_low

        bd_set_summary[seq]['medium']['psnr'] = avg_psnr_bd_medium
        bd_set_summary[seq]['medium']['ssim'] = avg_ssim_bd_medium
        bd_set_summary[seq]['medium']['vmaf'] = vmaf_bd_medium

        bd_set_summary[seq]['high']['psnr'] = avg_psnr_bd_high
        bd_set_summary[seq]['high']['ssim'] = avg_ssim_bd_high
        bd_set_summary[seq]['high']['vmaf'] = vmaf_bd_high

        i = 0
        for qp_r in qp_rate:
            worksheet.write(r, 2, qp_r)
            test_data = [v[0][i], compute_psnr(v[1][i]),
                         compute_psnr(v[2][i]),
                         compute_psnr(v[3][i]),
                         v[4][i], v[5][i], v[6][i], v[7][i]]

            anchor_data = [v[8][i], compute_psnr(v[9][i]),
                           compute_psnr(v[10][i]),
                           compute_psnr(v[11][i]),
                           v[12][i], v[13][i], v[14][i], v[15][i]]

            c = 3
            for d in anchor_data:
                write_val_to_worsheet(worksheet, r, c, d, f_pos,
                                      f_neg, f_cell)
                c = c + 1
            c = 11
            for d in test_data:
                write_val_to_worsheet(worksheet, r, c, d, f_pos,
                                      f_neg, f_cell)
                c = c + 1

            if (i == 0):
                write_val_to_worsheet(worksheet, r, c, avg_psnr_bd_low,
                                      f_pos, f_neg, f_cell)
                write_val_to_worsheet(worksheet, r, c + 1, avg_ssim_bd_low,
                                      f_pos, f_neg, f_cell)
                write_val_to_worsheet(worksheet, r, c + 2, vmaf_bd_low,
                                      f_pos, f_neg, f_cell)
                write_val_to_worsheet(worksheet, r, c + 3, 'low',
                                      f_pos, f_neg, f_cell)
            if (i == 1):
                write_val_to_worsheet(worksheet, r, c, avg_psnr_bd_medium,
                                      f_pos, f_neg, f_cell)
                write_val_to_worsheet(worksheet, r, c + 1, avg_ssim_bd_medium,
                                      f_pos, f_neg, f_cell)
                write_val_to_worsheet(worksheet, r, c + 2, vmaf_bd_medium,
                                      f_pos, f_neg, f_cell)
                write_val_to_worsheet(worksheet, r, c + 3, 'medium',
                                      f_pos, f_neg, f_cell)
            if (i == 2):
                write_val_to_worsheet(worksheet, r, c, avg_psnr_bd_high,
                                      f_pos, f_neg, f_cell)
                write_val_to_worsheet(worksheet, r, c + 1, avg_ssim_bd_high,
                                      f_pos, f_neg, f_cell)
                write_val_to_worsheet(worksheet, r, c + 2, vmaf_bd_high,
                                      f_pos, f_neg, f_cell)
                write_val_to_worsheet(worksheet, r, c + 3, 'high',
                                      f_pos, f_neg, f_cell)

            if (i == 3):
                write_val_to_worsheet(worksheet, r, c, avg_psnr_bd_all,
                                      f_pos, f_neg, f_cell)
                write_val_to_worsheet(worksheet, r, c + 1, avg_ssim_bd_all,
                                      f_pos, f_neg, f_cell)
                write_val_to_worsheet(worksheet, r, c + 2, vmaf_bd_all,
                                      f_pos, f_neg, f_cell)
                write_val_to_worsheet(worksheet, r, c + 3, 'all',
                                      f_pos, f_neg, f_cell)

            r = r + 1
            i = i + 1


def add_chart(workbook, sequence_name, y_axis_name):
    chart = workbook.add_chart({'type': 'scatter',
                                'subtype': 'straight_with_markers'})

    chart.set_title({
        'name': sequence_name,
        'overlay': True,
        'layout': {
            'x': 0.30,
            'y': 0.0,
        },
        'name_font': {
            'name': 'Consolas',
            'color': 'black',
            'size': 10,
            'bold': 0,
            'italic': 0
        }
    })
    chart.set_x_axis({
        'name': 'Bit Rate (Kbps)',
        'name_layout': {
            'x': 0.34,
            'y': 0.85,
        },
        'min': 0,
        'num_format': '#',
    })
    chart.set_y_axis({
        'name': y_axis_name,
        'name_layout': {
            'x': 0.01,
            'y': 0.35,
        },
        'min': 20,
        'max': 50,
    })

    return chart


def add_chart_psnr(workbook, sequence_name):
    """Create a chart optimized for PSNR values (typically 20-50 dB)"""
    chart = workbook.add_chart({'type': 'scatter',
                                'subtype': 'straight_with_markers'})

    chart.set_title({
        'name': sequence_name,
        'overlay': True,
        'layout': {
            'x': 0.30,
            'y': 0.0,
        },
        'name_font': {
            'name': 'Consolas',
            'color': 'black',
            'size': 10,
            'bold': 0,
            'italic': 0
        }
    })
    chart.set_x_axis({
        'name': 'Bit Rate (Kbps)',
        'name_layout': {
            'x': 0.34,
            'y': 0.85,
        },
        'min': 0,
        'num_format': '#',
    })
    chart.set_y_axis({
        'name': 'Avg PSNR (dB)',
        'name_layout': {
            'x': 0.01,
            'y': 0.35,
        },
        'min': 20,
        'max': 50,
        'num_format': '0.0',
    })
    chart.set_size({'width': 480, 'height': 288})  # Standard size

    return chart


def add_chart_vmaf(workbook, sequence_name):
    """Create a chart optimized for VMAF values (typically 70-100)"""
    chart = workbook.add_chart({'type': 'scatter',
                                'subtype': 'straight_with_markers'})

    chart.set_title({
        'name': sequence_name,
        'overlay': True,
        'layout': {
            'x': 0.30,
            'y': 0.0,
        },
        'name_font': {
            'name': 'Consolas',
            'color': 'black',
            'size': 10,
            'bold': 0,
            'italic': 0
        }
    })
    chart.set_x_axis({
        'name': 'Bit Rate (Kbps)',
        'name_layout': {
            'x': 0.34,
            'y': 0.85,
        },
        'min': 0,
        'num_format': '#',
    })
    chart.set_y_axis({
        'name': 'VMAF Score',
        'name_layout': {
            'x': 0.01,
            'y': 0.35,
        },
        'min': 70,   # Start from 70 for better visibility
        'max': 100,  # VMAF max is 100
        'num_format': '0.0',
    })
    chart.set_size({'width': 480, 'height': 288})  # Standard size

    return chart


def add_graph_line(x, y, worksheet_name, chart, series_name):
    cell_range_x = xl_range_abs(x[0], x[1], x[2], x[3])
    cell_range_y = xl_range_abs(y[0], y[1], y[2], y[3])

    range_string_x = '=\'' + worksheet_name + '\'!' + cell_range_x
    range_string_y = '=\'' + worksheet_name + '\'!' + cell_range_y
    series = {}
    marker = {}
    series['name'] = series_name
    series['categories'] = range_string_x
    series['values'] = range_string_y
    marker['type'] = 'automatic'
    marker['size'] = 3
    series['marker'] = marker

    chart.add_series(series)


def generate_graph_worksheet(workbook, worksheet, worksheet_list, ref_type, rdo, reference_name, row_index,
                             input_list, sanity_check_evaluated_points):
    row_increment = sanity_check_evaluated_points  # data points

    if (row_index == 0):  # header
        pass
    print("start ", worksheet.name)

    row_index = row_index + 2

    s_row_data = 3

    s_col_ref_data_br = 3
    s_col_ref_data_pnsr = 4
    s_col_enc_data_br = 11
    s_col_enc_data_pnsr = 12
    for seq in input_list:
        seq = '_'.join(seq.split('_')[:-1])
        chart = add_chart(workbook, seq, 'Avg PSNR (dB)')
        count = 0
        for encoder in rdo:

            worksheet_name = encoder + " {}".format(ref_type.upper())

            for source_worksheet in worksheet_list:
                if (source_worksheet.name.find(worksheet_name) != -1):
                    break

            if (count == 0):
                x = [s_row_data, s_col_ref_data_br, s_row_data + row_increment - 1, s_col_ref_data_br]
                y = [s_row_data, s_col_ref_data_pnsr, s_row_data + row_increment - 1, s_col_ref_data_pnsr]
                add_graph_line(x, y, source_worksheet.name, chart, reference_name)

            x = [s_row_data, s_col_enc_data_br, s_row_data + row_increment - 1, s_col_enc_data_br]
            y = [s_row_data, s_col_enc_data_pnsr, s_row_data + row_increment - 1, s_col_enc_data_pnsr]
            add_graph_line(x, y, source_worksheet.name, chart, source_worksheet.name)

            count = count + 1

        cell = xl_rowcol_to_cell(row_index, 2)
        worksheet.insert_chart(cell, chart)
        s_row_data = s_row_data + row_increment

        row_index = row_index + 15

    return row_index


def generate_combined_graph_worksheet(workbook, worksheet, worksheet_list, ref_type, rdo, reference_name, row_index,
                                     input_list, sanity_check_evaluated_points, simulation_graphs, main_group):
    """
    Generate combined RD curves with both PSNR and VMAF graphs for each sequence
    """
    row_increment = sanity_check_evaluated_points  # data points

    if (row_index == 0):  # header
        pass
    print("start ", worksheet.name)

    # Add title
    worksheet.write(0, 0, f'RD Curves - {ref_type.upper()} (PSNR & VMAF)')
    row_index = row_index + 2

    s_row_data = 3

    # Column positions for data
    s_col_ref_data_br = 3
    s_col_ref_data_psnr = 4   # Y PSNR column for reference
    s_col_ref_data_vmaf = 10  # VMAF column for reference
    s_col_enc_data_br = 11
    s_col_enc_data_psnr = 12  # Y PSNR column for encoder
    s_col_enc_data_vmaf = 18  # VMAF column for encoder

    for seq in input_list:
        seq = '_'.join(seq.split('_')[:-1])

        # Create PSNR chart
        psnr_chart = add_chart(workbook, seq + ' - PSNR', 'Avg PSNR (dB)')

        # Create VMAF chart
        vmaf_chart = add_chart(workbook, seq + ' - VMAF', 'VMAF Score')

        count = 0
        for encoder in rdo:
            worksheet_name = encoder + " {}".format(ref_type.upper())

            for source_worksheet in worksheet_list:
                if (source_worksheet.name.find(worksheet_name) != -1):
                    break

            # Add reference line only once (for first encoder)
            if (count == 0):
                # PSNR reference line
                x = [s_row_data, s_col_ref_data_br, s_row_data + row_increment - 1, s_col_ref_data_br]
                y = [s_row_data, s_col_ref_data_psnr, s_row_data + row_increment - 1, s_col_ref_data_psnr]
                add_graph_line(x, y, source_worksheet.name, psnr_chart, reference_name)

                # VMAF reference line
                x = [s_row_data, s_col_ref_data_br, s_row_data + row_increment - 1, s_col_ref_data_br]
                y = [s_row_data, s_col_ref_data_vmaf, s_row_data + row_increment - 1, s_col_ref_data_vmaf]
                add_graph_line(x, y, source_worksheet.name, vmaf_chart, reference_name)

            # Add encoder lines
            # PSNR encoder line
            x = [s_row_data, s_col_enc_data_br, s_row_data + row_increment - 1, s_col_enc_data_br]
            y = [s_row_data, s_col_enc_data_psnr, s_row_data + row_increment - 1, s_col_enc_data_psnr]
            add_graph_line(x, y, source_worksheet.name, psnr_chart, encoder)

            # VMAF encoder line
            x = [s_row_data, s_col_enc_data_br, s_row_data + row_increment - 1, s_col_enc_data_br]
            y = [s_row_data, s_col_enc_data_vmaf, s_row_data + row_increment - 1, s_col_enc_data_vmaf]
            add_graph_line(x, y, source_worksheet.name, vmaf_chart, encoder)

            count = count + 1

        # Insert both charts side by side
        psnr_cell = xl_rowcol_to_cell(row_index, 1)
        vmaf_cell = xl_rowcol_to_cell(row_index, 9)  # Place VMAF chart to the right

        worksheet.insert_chart(psnr_cell, psnr_chart)
        worksheet.insert_chart(vmaf_cell, vmaf_chart)

        s_row_data = s_row_data + row_increment
        row_index = row_index + 18  # More space for side-by-side charts

    return row_index





def generate_detailed_report_view(simulation_results, simulation_graphs, main_group, cfg_file_name, reference,
                                  reported_outputs,
                                  rdolevel, bitrate_list, crf_list, input_list, sanity_check_evaluated_points):
    workbook_name = main_group + '_' + cfg_file_name.split('.')[0] + '_' + reference
    workbook_filename = workbook_name + '.xlsx'
    workbook = xlsxwriter.Workbook(workbook_filename)

    start_time = time.time()

    print('generate_data_frame: {0:0.1f} min.seconds'.format((time.time() - start_time) / 60.0))

    # Add a format. Light red fill with dark red text.
    format1 = workbook.add_format({'bg_color': '#FFC7CE',
                                   'font_color': '#9C0006'})

    # Add a format. Green fill with dark green text.
    format2 = workbook.add_format({'bg_color': '#C6EFCE',
                                   'font_color': '#006100'})

    format_positive = {'type': 'cell',
                       'criteria': 'greater than',
                       'value': 0,
                       'format': format1}

    format_negative = {'type': 'cell',
                       'criteria': 'less than or equal to',
                       'value': 0,
                       'format': format2}

    number_format = workbook.add_format({'num_format': '0.00'})

    worksheets = []
    if ('h264' in reported_outputs):
        worksheet_name = 'Summary H.264'
        worksheets.append(workbook.add_worksheet(worksheet_name))

    if ('h265' in reported_outputs):
        worksheet_name = 'Summary H.265'
        worksheets.append(workbook.add_worksheet(worksheet_name))

    if ('av1' in reported_outputs):
        worksheet_name = 'Summary AV1'
        worksheets.append(workbook.add_worksheet(worksheet_name))

    if ('h264' in reported_outputs):
        worksheet_name = 'RD Curves H264'
        worksheets.append(workbook.add_worksheet(worksheet_name))

    if ('h265' in reported_outputs):
        worksheet_name = 'RD Curves H265'
        worksheets.append(workbook.add_worksheet(worksheet_name))

    if ('av1' in reported_outputs):
        worksheet_name = 'RD Curves AV1'
        worksheets.append(workbook.add_worksheet(worksheet_name))

    if ('h264' in reported_outputs):
        worksheet_name = 'Sequences H.264'
        worksheets.append(workbook.add_worksheet(worksheet_name))

    if ('h265' in reported_outputs):
        worksheet_name = 'Sequences H.265'
        worksheets.append(workbook.add_worksheet(worksheet_name))

    if ('av1' in reported_outputs):
        worksheet_name = 'Sequences AV1'
        worksheets.append(workbook.add_worksheet(worksheet_name))

    for encoder in rdolevel:
        if ('h264' in reported_outputs):
            worksheet_name = encoder + " H264"
            worksheets.append(workbook.add_worksheet(worksheet_name))

    for encoder in rdolevel:
        if ('h265' in reported_outputs):
            worksheet_name = encoder + " H265"
            worksheets.append(workbook.add_worksheet(worksheet_name))

    for encoder in rdolevel:
        if ('av1' in reported_outputs):
            worksheet_name = encoder + " AV1"
            worksheets.append(workbook.add_worksheet(worksheet_name))

    for worksheet in worksheets:
        if (worksheet.name.find('Summary') != -1):
            print(worksheet.name)

            if (worksheet.name.find('H.264') != -1):
                print('H.264', worksheet.name)
                generate_summary_worksheet(simulation_results, worksheet, format_positive,
                                           format_negative, number_format,
                                           'h264', main_group, rdolevel, reference, cfg_file_name)

            if (worksheet.name.find('H.265') != -1):
                print('H.265', worksheet.name)
                generate_summary_worksheet(simulation_results, worksheet, format_positive,
                                           format_negative, number_format,
                                           'h265', main_group, rdolevel, reference, cfg_file_name)

            if (worksheet.name.find('AV1') != -1):
                print('AV1', worksheet.name)
                generate_summary_worksheet(simulation_results, worksheet, format_positive,
                                           format_negative, number_format,
                                           'av1', main_group, rdolevel, reference, cfg_file_name)

        if (worksheet.name.find('Sequences') != -1):
            print(worksheet.name)

            if (worksheet.name.find('H.264') != -1):
                print('H.264', worksheet.name, 'All Ranges Combined')
                generate_summary_worksheet_per_sequence_all_ranges(simulation_results, worksheet, format_positive,
                                                                  format_negative, number_format,
                                                                  'h264', main_group, rdolevel, reference, cfg_file_name,
                                                                  input_list)

            if (worksheet.name.find('H.265') != -1):
                print('H.265', worksheet.name, 'All Ranges Combined')
                generate_summary_worksheet_per_sequence_all_ranges(simulation_results, worksheet, format_positive,
                                                                  format_negative, number_format,
                                                                  'h265', main_group, rdolevel, reference, cfg_file_name,
                                                                  input_list)

            if (worksheet.name.find('AV1') != -1):
                print('AV1', worksheet.name, 'All Ranges Combined')
                generate_summary_worksheet_per_sequence_all_ranges(simulation_results, worksheet, format_positive,
                                                                  format_negative, number_format,
                                                                  'av1', main_group, rdolevel, reference, cfg_file_name,
                                                                  input_list)

        for rdo in rdolevel:

            if (worksheet.name.find(rdo) != -1):

                if (worksheet.name.find('H264') != -1):
                    print('H.264', worksheet.name)
                    generate_detailed_worksheet(simulation_results, simulation_graphs, workbook, worksheet,
                                                format_positive,
                                                format_negative, number_format,
                                                'h264', main_group, rdo, reference, cfg_file_name, bitrate_list,
                                                crf_list, 'avc', input_list,sanity_check_evaluated_points)

                if (worksheet.name.find('H265') != -1):
                    print('H.265', worksheet.name)
                    generate_detailed_worksheet(simulation_results, simulation_graphs, workbook, worksheet,
                                                format_positive,
                                                format_negative, number_format,
                                                'h265', main_group, rdo, reference, cfg_file_name, bitrate_list,
                                                crf_list, 'hevc', input_list,sanity_check_evaluated_points)

                if (worksheet.name.find('AV1') != -1):
                    print('AV1', worksheet.name)
                    generate_detailed_worksheet(simulation_results, simulation_graphs, workbook, worksheet,
                                                format_positive,
                                                format_negative, number_format,
                                                'av1', main_group, rdo, reference, cfg_file_name, bitrate_list,
                                                crf_list, 'av1', input_list,sanity_check_evaluated_points)

    row_index_h264 = 0  # rows for summary graphs
    row_index_h265 = 0
    row_index_av1 = 0
    graph_worksheets = []

    for graph_worksheet in worksheets:
        if (graph_worksheet.name.find('RD Curves H264') != -1 or
                graph_worksheet.name.find('RD Curves H265') != -1 or
                graph_worksheet.name.find('RD Curves AV1') != -1):
            graph_worksheets.append(graph_worksheet)

    for worksheet in graph_worksheets:
        if (worksheet.name.find('H264') != -1):
            print('Generating H264 Combined RD Curves (PSNR & VMAF)')
            row_index_h264 = generate_combined_graph_worksheet(workbook, worksheet, worksheets, 'h264', rdolevel, reference,
                                                             row_index_h264, input_list, sanity_check_evaluated_points, simulation_graphs, main_group)

        if (worksheet.name.find('H265') != -1):
            print('Generating H265 Combined RD Curves (PSNR & VMAF)')
            row_index_h265 = generate_combined_graph_worksheet(workbook, worksheet, worksheets, 'h265', rdolevel, reference,
                                                             row_index_h265, input_list, sanity_check_evaluated_points, simulation_graphs, main_group)

        if (worksheet.name.find('AV1') != -1):
            print('Generating AV1 Combined RD Curves (PSNR & VMAF)')
            row_index_av1 = generate_combined_graph_worksheet(workbook, worksheet, worksheets, 'av1', rdolevel, reference,
                                                             row_index_av1, input_list, sanity_check_evaluated_points, simulation_graphs, main_group)

    workbook.close()